[ ] NAME:Current Task List DESCRIPTION:Root task for conversation 0b51537d-de26-419d-8cc3-b02f5defca3e
-[ ] NAME:重构出模型适配层 DESCRIPTION:需要分层架构，分离出大模型模型调用层；以便后续支持不同的模型API；具体行动：将 src/stock_ai/agents/base.py模块中 call_llm 方法，分离到
独立的包中，以便后续扩展不同模型API，优先支持非流式模型调用，后续再扩展流式调用；
- 增加新功能：大模型调用日志，但有别于传统日志，需记录为markdownwn格式，以便于查看；记录完整的大模型调用所用到的工具、消息(messages)、等参数、完整响应到md文件, 日志文件支持配置按大小、日期拆分。 自动滚动成多个，日志文件命名策略、保留策略可以按照传统日志框架等设定；
- 增加新功能：增加pg表：模型信息、模型用量信息；存储使用的模型（基础地址、apikey、模型id、其它配置信息等）；记录每次大模型的token使用量、关联的模型、用json字段完整记录下请求、响应，关联模型信息等，以便后续数据分析；
-[ ] NAME:在以上模型适配层分离到基础上，增加MCP协议支持 DESCRIPTION:支持MCP协议，以便于后续扩展MCP协议的支持，例如：支持MCP协议的市场信息获取、市场信息推送等；
