# 🤖 Stock AI Agents

A sophisticated multi-agent stock trading system powered by AI that automates investment decisions through specialized AI agents working in coordination.

## ✨ Features

### 🎯 Multi-Agent Architecture
- **Market Analyst Agent**: Analyzes market trends and economic indicators
- **Stock Selector Agent**: Identifies promising investment opportunities
- **Trading Decision Maker Agent**: Makes specific buy/sell decisions with risk management
- **Portfolio Manager Agent**: Manages overall portfolio allocation and performance
- **Risk Controller Agent**: Monitors and controls investment risks

### 📊 Advanced Capabilities
- Real-time market data analysis and technical indicators
- Automated trading decisions with comprehensive risk management
- Dynamic portfolio optimization and rebalancing
- Multi-modal coordination between agents (autonomous, collaborative, hierarchical)
- Market hours awareness with automatic pause/resume functionality
- Comprehensive decision recording and audit trails

### 🖥️ Interactive Interface
- Beautiful terminal interface with live dashboard
- Colorful output with rich formatting
- Real-time agent status monitoring
- Interactive agent management and control
- System pause/resume functionality
- Comprehensive logging and monitoring

### 🛡️ Risk Management
- Position sizing limits and concentration controls
- Stop-loss and take-profit automation
- Value at Risk (VaR) calculations
- Portfolio diversification monitoring
- Emergency stop functionality

## 🚀 Quick Start

### Installation

```bash
# Clone the repository
git clone https://github.com/stockai/stock-ai-agents.git
cd stock-ai-agents

# Install the package
pip install -e .

# Or install with development dependencies
pip install -e ".[dev]"
```

### Configuration

1. Copy the example environment file:
```bash
cp .env.example .env
```

2. Configure your settings in `.env`:
```bash
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4

# Database Configuration
DATABASE_URL=postgresql://localhost:5432/stock_ai

# Trading Configuration
TRADING_MODE=paper  # or 'live' for real trading
INITIAL_CAPITAL=100000.00
MAX_POSITION_SIZE=0.10  # 10% max per position
STOP_LOSS_PERCENTAGE=0.05  # 5% stop loss
TAKE_PROFIT_PERCENTAGE=0.15  # 15% take profit

# Market Hours (UTC)
MARKET_OPEN_HOUR=14  # 9:30 AM EST
MARKET_OPEN_MINUTE=30
MARKET_CLOSE_HOUR=21  # 4:00 PM EST
MARKET_CLOSE_MINUTE=0

# Notifications (optional)
NOTIFICATIONS_ENABLED=true
NOTIFICATIONS_WEBHOOK_URL=your_slack_webhook_url
NOTIFICATIONS_LEVEL=WARNING
```

### Database Setup

Initialize the database:
```bash
stock-ai init-db
```

Test database connection:
```bash
stock-ai test-db
```

## 🎮 Usage

### Interactive Mode (Recommended)

Start the interactive terminal interface:
```bash
stock-ai interactive
```

This provides a full-featured terminal interface with:
- Live dashboard with real-time agent status
- Agent management (pause/resume individual agents)
- System control (start/stop/pause entire system)
- Coordination management
- Real-time monitoring and logging

### Command Line Mode

Start the system directly:
```bash
# Start with collaborative coordination (default)
stock-ai start

# Start with different coordination modes
stock-ai start --mode autonomous
stock-ai start --mode hierarchical

# Enable debug mode
stock-ai start --debug

# Use mock market data for testing
stock-ai start --mock-data
```

### System Status

Check system status:
```bash
stock-ai status
```

## 🏗️ Architecture

### Agent Coordination Modes

1. **Autonomous Mode**: Agents work independently with minimal coordination
2. **Collaborative Mode**: Agents share information and collaborate on decisions
3. **Hierarchical Mode**: Agents follow a chain of command structure

### Data Flow

```
Market Data → Market Analyst → Stock Selector → Trading Decision Maker
                    ↓              ↓                    ↓
              Portfolio Manager ← Risk Controller ← Coordination Service
```

### Key Components

- **Agent Manager**: Manages lifecycle of all AI agents
- **Coordination Service**: Facilitates multi-agent communication
- **Scheduler Service**: Manages market hours and system state
- **Database Layer**: Persistent storage for decisions, transactions, and market data
- **Tools System**: Modular tools for market data, risk analysis, and notifications

## 📈 Trading Strategy

The system implements a multi-layered approach:

1. **Market Analysis**: Continuous monitoring of market trends and sentiment
2. **Stock Selection**: Screening and analysis of individual stocks
3. **Decision Making**: Risk-adjusted buy/sell decisions with proper sizing
4. **Portfolio Management**: Overall allocation and rebalancing
5. **Risk Control**: Continuous monitoring and risk mitigation

## 🔧 Development

### Setup Development Environment

```bash
# Install with development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest

# Run tests with coverage
pytest --cov=src/stock_ai

# Format code
black src/ tests/
isort src/ tests/

# Type checking
mypy src/
```

### Project Structure

```
stock-ai-agents/
├── src/stock_ai/           # Main package
│   ├── agents/             # AI agent implementations
│   ├── models/             # Database models
│   ├── tools/              # Agent tools and capabilities
│   ├── services/           # Core services
│   ├── utils/              # Utilities
│   ├── config.py           # Configuration management
│   ├── database.py         # Database setup
│   ├── main.py             # CLI entry point
│   └── terminal.py         # Interactive terminal
├── tests/                  # Test suite
├── alembic/                # Database migrations
├── data/                   # Data storage (decisions, plans)
├── logs/                   # Log files
└── docs/                   # Documentation
```

### Adding New Agents

1. Create agent class inheriting from `BaseAgent`
2. Define system prompt and tools
3. Implement `execute_cycle()` method
4. Register in `AgentManager`

### Adding New Tools

1. Create tool class inheriting from `BaseTool`
2. Define tool parameters and schema
3. Implement `execute()` method
4. Add to agent tool lists

## 📊 Monitoring and Logging

### Log Files

- `logs/stock_ai.log` - Main application log
- `logs/agents.log` - Agent-specific activities
- `logs/trading.log` - Trading decisions and transactions
- `logs/risk.log` - Risk management events

### Decision Records

All trading decisions are automatically saved as markdown files in `data/decisions/` with:
- Decision rationale and reasoning
- Market conditions at time of decision
- Risk assessment and mitigation strategies
- Technical indicators and analysis

### Investment Plans

Portfolio management plans are saved in `data/plans/` with:
- Strategic objectives and targets
- Asset allocation guidelines
- Performance tracking
- Regular updates and reviews

## ⚠️ Risk Disclaimer

This software is for educational and research purposes only. Trading stocks involves substantial risk of loss and is not suitable for all investors. Past performance does not guarantee future results. Always consult with a qualified financial advisor before making investment decisions.

**Important**: 
- Start with paper trading mode to test strategies
- Never invest more than you can afford to lose
- Understand the risks involved in algorithmic trading
- Monitor the system regularly and maintain appropriate safeguards

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](CONTRIBUTING.md) for details.

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- OpenAI for providing the GPT models
- The Python community for excellent libraries
- Financial data providers for market data access
- Contributors and testers who help improve the system

## 📞 Support

- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/stockai)
- 📖 Documentation: [Full documentation](https://stockai.github.io/stock-ai-agents)
- 🐛 Issues: [GitHub Issues](https://github.com/stockai/stock-ai-agents/issues)

---

**Made with ❤️ by the Stock AI Team**
