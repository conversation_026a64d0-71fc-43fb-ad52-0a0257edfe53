#!/usr/bin/env python3
"""
Stock AI Agents Web Interface Demo

This script demonstrates the web interface functionality by making API calls
and showing the responses.
"""

import asyncio
import aiohttp
import json
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn

console = Console()

BASE_URL = "http://localhost:8000"

async def test_api_endpoint(session, endpoint, method="GET", data=None):
    """Test a single API endpoint"""
    url = f"{BASE_URL}{endpoint}"
    
    try:
        if method == "GET":
            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"HTTP {response.status}"}
        elif method == "POST":
            async with session.post(url, json=data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    return {"error": f"HTTP {response.status}"}
    except Exception as e:
        return {"error": str(e)}

async def demo_system_status():
    """Demo system status API"""
    console.print("\n[bold blue]🔍 System Status Demo[/bold blue]")
    
    async with aiohttp.ClientSession() as session:
        status = await test_api_endpoint(session, "/api/status")
        
        if "error" not in status:
            table = Table(title="System Status")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Status", status.get("status", "Unknown"))
            table.add_row("Uptime", status.get("uptime", "Unknown"))
            table.add_row("Total Agents", str(status.get("agents_count", 0)))
            table.add_row("Active Agents", str(status.get("active_agents", 0)))
            table.add_row("Last Update", status.get("last_update", "Unknown"))
            
            console.print(table)
        else:
            console.print(f"[red]Error: {status['error']}[/red]")

async def demo_agents():
    """Demo agents API"""
    console.print("\n[bold blue]🤖 Agents Demo[/bold blue]")
    
    async with aiohttp.ClientSession() as session:
        agents = await test_api_endpoint(session, "/api/agents")
        
        if "error" not in agents and isinstance(agents, list):
            table = Table(title="AI Agents Status")
            table.add_column("Name", style="cyan")
            table.add_column("Type", style="blue")
            table.add_column("Status", style="green")
            table.add_column("Executions", style="yellow")
            table.add_column("Errors", style="red")
            
            for agent in agents:
                status_color = "green" if agent.get("status") == "ACTIVE" else "red"
                table.add_row(
                    agent.get("name", "Unknown"),
                    agent.get("type", "Unknown"),
                    f"[{status_color}]{agent.get('status', 'Unknown')}[/{status_color}]",
                    str(agent.get("execution_count", 0)),
                    str(agent.get("error_count", 0))
                )
            
            console.print(table)
        else:
            console.print(f"[red]Error: {agents.get('error', 'Unknown error')}[/red]")

async def demo_portfolio():
    """Demo portfolio API"""
    console.print("\n[bold blue]💼 Portfolio Demo[/bold blue]")
    
    async with aiohttp.ClientSession() as session:
        # Get portfolio summary
        portfolio = await test_api_endpoint(session, "/api/portfolio")
        
        if "error" not in portfolio:
            table = Table(title="Portfolio Summary")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Total Value", f"¥{portfolio.get('total_value', 0):,.2f}")
            table.add_row("Cash Balance", f"¥{portfolio.get('cash_balance', 0):,.2f}")
            table.add_row("Positions Count", str(portfolio.get('positions_count', 0)))
            table.add_row("Daily P&L", f"¥{portfolio.get('daily_pnl', 0):,.2f}")
            table.add_row("Daily P&L %", f"{portfolio.get('daily_pnl_percent', 0):.2f}%")
            table.add_row("Total P&L", f"¥{portfolio.get('total_pnl', 0):,.2f}")
            table.add_row("Total P&L %", f"{portfolio.get('total_pnl_percent', 0):.2f}%")
            
            console.print(table)
        else:
            console.print(f"[red]Error: {portfolio['error']}[/red]")
        
        # Get positions
        positions = await test_api_endpoint(session, "/api/portfolio/positions")
        
        if "error" not in positions and "positions" in positions:
            table = Table(title="Current Positions")
            table.add_column("Symbol", style="cyan")
            table.add_column("Name", style="blue")
            table.add_column("Quantity", style="yellow")
            table.add_column("Current Price", style="green")
            table.add_column("Market Value", style="green")
            table.add_column("P&L", style="red")
            
            for pos in positions["positions"][:5]:  # Show first 5 positions
                pnl_color = "green" if pos.get("pnl", 0) >= 0 else "red"
                table.add_row(
                    pos.get("symbol", "Unknown"),
                    pos.get("name", "Unknown"),
                    f"{pos.get('quantity', 0):,}",
                    f"¥{pos.get('current_price', 0):.2f}",
                    f"¥{pos.get('market_value', 0):,.2f}",
                    f"[{pnl_color}]¥{pos.get('pnl', 0):,.2f}[/{pnl_color}]"
                )
            
            console.print(table)

async def demo_market_data():
    """Demo market data APIs"""
    console.print("\n[bold blue]📈 Market Data Demo[/bold blue]")
    
    async with aiohttp.ClientSession() as session:
        # Get market news
        news = await test_api_endpoint(session, "/api/market/news?limit=3")
        
        if "error" not in news and "articles" in news:
            console.print("\n[bold yellow]📰 Latest Market News[/bold yellow]")
            for i, article in enumerate(news["articles"], 1):
                sentiment_color = {
                    "POSITIVE": "green",
                    "NEGATIVE": "red",
                    "NEUTRAL": "yellow"
                }.get(article.get("sentiment", "NEUTRAL"), "yellow")
                
                panel = Panel(
                    f"[bold]{article.get('title', 'No title')}[/bold]\n\n"
                    f"{article.get('summary', 'No summary')}\n\n"
                    f"Source: {article.get('source', 'Unknown')} | "
                    f"Sentiment: [{sentiment_color}]{article.get('sentiment', 'NEUTRAL')}[/{sentiment_color}]",
                    title=f"News {i}",
                    border_style="blue"
                )
                console.print(panel)
        
        # Get market sentiment
        sentiment = await test_api_endpoint(session, "/api/market/sentiment")
        
        if "error" not in sentiment:
            console.print("\n[bold yellow]💭 Market Sentiment[/bold yellow]")
            
            overall_color = {
                "BULLISH": "green",
                "BEARISH": "red",
                "NEUTRAL": "yellow"
            }.get(sentiment.get("overall_sentiment", "NEUTRAL"), "yellow")
            
            table = Table(title="Sentiment Analysis")
            table.add_column("Metric", style="cyan")
            table.add_column("Value", style="green")
            
            table.add_row("Overall Sentiment", 
                         f"[{overall_color}]{sentiment.get('overall_sentiment', 'NEUTRAL')}[/{overall_color}]")
            table.add_row("Sentiment Score", str(sentiment.get("sentiment_score", 0)))
            table.add_row("Positive Articles", str(sentiment.get("sentiment_distribution", {}).get("POSITIVE", 0)))
            table.add_row("Neutral Articles", str(sentiment.get("sentiment_distribution", {}).get("NEUTRAL", 0)))
            table.add_row("Negative Articles", str(sentiment.get("sentiment_distribution", {}).get("NEGATIVE", 0)))
            table.add_row("Total Articles", str(sentiment.get("total_articles", 0)))
            table.add_row("Confidence", sentiment.get("confidence", "Unknown"))
            
            console.print(table)

async def demo_agent_control():
    """Demo agent control APIs"""
    console.print("\n[bold blue]🎮 Agent Control Demo[/bold blue]")
    
    async with aiohttp.ClientSession() as session:
        # Try to start an agent
        console.print("Attempting to start 'market_analyst' agent...")
        result = await test_api_endpoint(session, "/api/agents/market_analyst/start", "POST")
        
        if "error" not in result:
            console.print(f"[green]✓ {result.get('message', 'Success')}[/green]")
        else:
            console.print(f"[red]✗ {result['error']}[/red]")
        
        # Try to stop an agent
        console.print("Attempting to stop 'market_analyst' agent...")
        result = await test_api_endpoint(session, "/api/agents/market_analyst/stop", "POST")
        
        if "error" not in result:
            console.print(f"[green]✓ {result.get('message', 'Success')}[/green]")
        else:
            console.print(f"[red]✗ {result['error']}[/red]")

async def main():
    """Main demo function"""
    console.print(Panel.fit(
        "[bold blue]Stock AI Agents Web Interface Demo[/bold blue]\n"
        "This demo will test various API endpoints and display the results.\n"
        f"Make sure the web server is running at {BASE_URL}",
        title="Demo Starting",
        border_style="green"
    ))
    
    # Test if server is running
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{BASE_URL}/api/status") as response:
                if response.status != 200:
                    console.print(f"[red]Error: Server not responding (HTTP {response.status})[/red]")
                    console.print("Please start the web server with: stock-ai web")
                    return
    except Exception as e:
        console.print(f"[red]Error: Cannot connect to server - {e}[/red]")
        console.print("Please start the web server with: stock-ai web")
        return
    
    console.print("[green]✓ Server is running![/green]")
    
    # Run demos
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console,
    ) as progress:
        
        demos = [
            ("Testing System Status API...", demo_system_status),
            ("Testing Agents API...", demo_agents),
            ("Testing Portfolio API...", demo_portfolio),
            ("Testing Market Data APIs...", demo_market_data),
            ("Testing Agent Control APIs...", demo_agent_control),
        ]
        
        for description, demo_func in demos:
            task = progress.add_task(description, total=None)
            await demo_func()
            progress.remove_task(task)
    
    console.print(Panel.fit(
        "[bold green]Demo completed successfully![/bold green]\n\n"
        "You can now:\n"
        f"• Visit the web interface at {BASE_URL}\n"
        f"• Check the API documentation at {BASE_URL}/api/docs\n"
        "• Explore the different pages and features",
        title="Demo Complete",
        border_style="green"
    ))

if __name__ == "__main__":
    asyncio.run(main())
