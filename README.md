# Stock AI Agents - 中国A股智能体交易系统

一个专门针对中国A股市场的多智能体架构股票交易系统，通过AI驱动的决策制定来最大化投资收益。

## 系统特性

- **多智能体协作**: 包含选股、买卖决策、市场分析等专业化智能体
- **持续运行**: 自动化交易决策，无需人工干预
- **智能暂停**: 支持市场时间感知的暂停/恢复机制
- **决策记录**: 完整记录所有交易决策的依据和过程
- **动态计划**: 智能体可以动态调整投资策略和计划
- **彩色终端**: 丰富的终端界面，支持彩色输出和状态监控

## 智能体角色

### 1. 市场分析智能体 (Market Analyst)
- 分析A股市场趋势和中国宏观经济指标
- 监控上证指数、深证成指、创业板指等主要指数
- 识别A股市场机会和风险
- 提供A股市场情绪分析

### 2. 选股智能体 (Stock Selector)
- 基于技术分析和基本面分析选择A股股票
- 评估A股股票的投资价值和风险
- 维护A股候选股票池
- 关注沪深两市优质标的

### 3. 买卖决策智能体 (Trading Decision Maker)
- 制定具体的买卖决策
- 确定交易时机和数量
- 管理风险和止损策略

### 4. 投资组合管理智能体 (Portfolio Manager)
- 管理整体投资组合
- 优化资产配置
- 监控投资组合表现

### 5. 风险控制智能体 (Risk Controller)
- 监控投资风险
- 执行风险控制策略
- 提供风险预警

## 技术架构

- **编程语言**: Python 3.9+
- **AI模型**: OpenAI兼容接口
- **数据库**: PostgreSQL
- **工具支持**: MCP协议 + 自定义工具
- **异步框架**: asyncio + aiohttp
- **终端界面**: Rich + Colorama

## 项目结构

```
stock_ai/
├── src/stock_ai/           # 主要源代码
│   ├── agents/            # 智能体实现
│   ├── tools/             # 工具集合
│   ├── database/          # 数据库相关
│   ├── models/            # 数据模型
│   ├── services/          # 业务服务
│   ├── utils/             # 工具函数
│   └── main.py           # 主入口
├── tests/                 # 测试代码
├── docs/                  # 文档
├── scripts/               # 脚本文件
├── data/                  # 数据文件
│   ├── decisions/         # 决策记录(MD文件)
│   └── plans/            # 投资计划(MD文件)
└── config/               # 配置文件
```

## 快速开始

### 1. 环境准备

#### 系统要求
- Python 3.9+
- PostgreSQL 12+
- 至少4GB内存
- 稳定的网络连接（用于获取市场数据）

#### 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd stock_ai

# 创建虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -e .
```

### 2. 数据库配置

#### 安装PostgreSQL
```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# macOS (使用Homebrew)
brew install postgresql

# Windows: 下载并安装PostgreSQL官方安装包
```

#### 创建数据库
```bash
# 创建数据库用户和数据库
sudo -u postgres psql
CREATE USER stock_ai_user WITH PASSWORD 'your_password';
CREATE DATABASE stock_ai OWNER stock_ai_user;
GRANT ALL PRIVILEGES ON DATABASE stock_ai TO stock_ai_user;
\q
```

### 3. 配置环境变量

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：

#### 必需配置
```bash
# OpenAI API配置（必需）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# 数据库配置（必需）
DATABASE_URL=postgresql://stock_ai_user:your_password@localhost:5432/stock_ai
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=stock_ai
DATABASE_USER=stock_ai_user
DATABASE_PASSWORD=your_password
```

#### 交易配置（可选）
```bash
# 交易模式：SIMULATION（模拟）或 LIVE（实盘，谨慎使用）
TRADING_MODE=SIMULATION

# 初始资金（人民币）
INITIAL_CAPITAL=1000000.0  # 100万人民币

# 风险管理参数
MAX_POSITION_SIZE=0.1      # 单个股票最大仓位10%
STOP_LOSS_PERCENTAGE=0.05  # 止损5%
TAKE_PROFIT_PERCENTAGE=0.15 # 止盈15%
MAX_DAILY_LOSS=0.02        # 日最大亏损2%
MAX_DRAWDOWN=0.1           # 最大回撤10%
```

#### A股市场时间配置（已预设）
```bash
# 中国A股交易时间（UTC时间）
MARKET_OPEN_HOUR=1    # 9:30 AM CST = 1:30 AM UTC
MARKET_OPEN_MINUTE=30
MARKET_CLOSE_HOUR=7   # 3:00 PM CST = 7:00 AM UTC
MARKET_CLOSE_MINUTE=0
```

### 4. 初始化系统

#### 初始化数据库
```bash
# 初始化数据库表和基础数据
stock-ai init-db
```

这将创建：
- 数据库表结构
- 默认交易账户（100万人民币初始资金）
- 样本A股股票数据（平安银行、万科A、贵州茅台等）
- 默认智能体配置

#### 测试数据库连接
```bash
stock-ai test-db
```

### 5. 运行系统

#### 启动交易系统
```bash
# 启动完整系统（推荐）
stock-ai start

# 或使用交互式模式
stock-ai interactive

# 调试模式
stock-ai start --debug

# 使用模拟数据
stock-ai start --mock-data
```

#### 系统状态检查
```bash
# 查看系统状态
stock-ai status
```

## 个性化配置

### 初始化账户设置

系统默认创建一个100万人民币的模拟交易账户。如需自定义：

#### 修改初始资金
在 `.env` 文件中调整：
```bash
INITIAL_CAPITAL=500000.0  # 50万人民币
```

#### 自定义风险参数
```bash
# 风险管理参数
MAX_POSITION_SIZE=0.05     # 单个股票最大仓位5%（更保守）
STOP_LOSS_PERCENTAGE=0.03  # 止损3%（更严格）
TAKE_PROFIT_PERCENTAGE=0.20 # 止盈20%（更激进）
MAX_DAILY_LOSS=0.01        # 日最大亏损1%（更保守）
```

### 投资期限设置

系统支持不同的投资策略和期限：

#### 短线交易配置
```bash
# 智能体更新频率（秒）
AGENT_UPDATE_INTERVAL=30   # 30秒更新一次
DECISION_COOLDOWN=60       # 1分钟决策冷却期

# 技术指标偏好短期
# 在智能体配置中会自动调整为短期指标
```

#### 中长线投资配置
```bash
# 智能体更新频率（秒）
AGENT_UPDATE_INTERVAL=300  # 5分钟更新一次
DECISION_COOLDOWN=1800     # 30分钟决策冷却期

# 基本面分析权重更高
# 系统会更关注公司财务数据和行业趋势
```

### 股票池自定义

#### 添加自定义股票
系统初始化后，可以通过数据库添加更多A股股票：

```sql
INSERT INTO stocks (symbol, name, exchange, sector, industry, currency, is_active, is_tradable)
VALUES
('000001.SZ', '平安银行', 'SZSE', '金融', '银行', 'CNY', true, true),
('600036.SH', '招商银行', 'SSE', '金融', '银行', 'CNY', true, true);
```

#### 行业偏好设置
可以在智能体配置中设置行业偏好：
- 科技股（新能源、人工智能、半导体）
- 消费股（白酒、食品饮料、医药）
- 金融股（银行、保险、证券）
- 周期股（钢铁、化工、建材）

### 通知设置

#### 启用微信/钉钉通知
```bash
ENABLE_NOTIFICATIONS=true
NOTIFICATION_WEBHOOK_URL=your_webhook_url_here
NOTIFICATION_LEVEL=INFO  # 接收所有重要信息
```

#### 通知内容自定义
- 交易执行通知
- 重要市场事件提醒
- 风险预警
- 每日收益报告

## 高级配置

### 多账户管理
系统支持管理多个交易账户：

```bash
# 创建新账户
stock-ai create-account --name "growth_account" --capital 2000000 --strategy "growth"
stock-ai create-account --name "value_account" --capital 1500000 --strategy "value"
```

### 策略模板
系统提供多种预设策略模板：

1. **稳健型**：低风险，注重资本保值
2. **平衡型**：中等风险，追求稳定收益
3. **成长型**：较高风险，追求资本增值
4. **激进型**：高风险高收益，适合经验丰富的投资者

## 配置说明

系统支持通过环境变量或配置文件进行配置：

### 核心配置
- `OPENAI_API_KEY`: OpenAI API密钥（必需）
- `DATABASE_URL`: PostgreSQL数据库连接URL（必需）
- `TRADING_MODE`: 交易模式 (SIMULATION, LIVE)
- `INITIAL_CAPITAL`: 初始资金（人民币）

### 风险管理
- `MAX_POSITION_SIZE`: 单个股票最大仓位比例
- `STOP_LOSS_PERCENTAGE`: 止损百分比
- `TAKE_PROFIT_PERCENTAGE`: 止盈百分比
- `MAX_DAILY_LOSS`: 日最大亏损比例
- `MAX_DRAWDOWN`: 最大回撤比例

### 系统运行
- `LOG_LEVEL`: 日志级别 (DEBUG, INFO, WARNING, ERROR)
- `AGENT_UPDATE_INTERVAL`: 智能体更新间隔（秒）
- `DECISION_COOLDOWN`: 决策冷却时间（秒）

## 故障排除

### 常见问题

#### 1. 数据库连接失败
**错误信息**: `could not connect to server: Connection refused`

**解决方案**:
```bash
# 检查PostgreSQL是否运行
sudo systemctl status postgresql  # Linux
brew services list | grep postgresql  # macOS

# 启动PostgreSQL
sudo systemctl start postgresql  # Linux
brew services start postgresql  # macOS

# 检查数据库是否存在
psql -U stock_ai_user -d stock_ai -h localhost
```

#### 2. OpenAI API密钥错误
**错误信息**: `Incorrect API key provided`

**解决方案**:
1. 检查 `.env` 文件中的 `OPENAI_API_KEY` 是否正确
2. 确认API密钥有足够的余额
3. 检查API密钥权限设置

#### 3. 市场数据获取失败
**错误信息**: `Failed to fetch market data`

**解决方案**:
```bash
# 检查网络连接
ping finance.yahoo.com

# 测试数据源
stock-ai test-data-source

# 切换到备用数据源
# 在.env中设置: MARKET_DATA_PROVIDER=alpha_vantage
```

#### 4. 智能体启动失败
**错误信息**: `Agent failed to initialize`

**解决方案**:
1. 检查日志文件 `logs/stock_ai.log`
2. 确认所有依赖已正确安装
3. 重新初始化数据库：`stock-ai init-db --force`

#### 5. 内存不足
**错误信息**: `MemoryError` 或系统响应缓慢

**解决方案**:
```bash
# 减少并发智能体数量
MAX_CONCURRENT_AGENTS=3  # 在.env中设置

# 增加系统交换空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 日志分析

#### 查看系统日志
```bash
# 查看主日志
tail -f logs/stock_ai.log

# 查看智能体日志
tail -f logs/agents.log

# 查看交易日志
tail -f logs/trading.log

# 查看风险管理日志
tail -f logs/risk.log
```

#### 日志级别说明
- `DEBUG`: 详细的调试信息
- `INFO`: 一般信息，系统正常运行
- `WARNING`: 警告信息，需要注意但不影响运行
- `ERROR`: 错误信息，可能影响系统功能
- `CRITICAL`: 严重错误，系统可能无法继续运行

### 性能优化

#### 数据库优化
```sql
-- 创建索引以提高查询性能
CREATE INDEX idx_market_data_symbol_timestamp ON market_data(symbol, timestamp);
CREATE INDEX idx_transactions_timestamp ON transactions(timestamp);
CREATE INDEX idx_positions_symbol ON positions(symbol);
```

#### 系统监控
```bash
# 监控系统资源使用
htop

# 监控数据库连接
SELECT count(*) FROM pg_stat_activity WHERE datname = 'stock_ai';

# 监控智能体状态
stock-ai status --detailed
```

### 备份与恢复

#### 数据库备份
```bash
# 创建备份
pg_dump -U stock_ai_user -h localhost stock_ai > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
psql -U stock_ai_user -h localhost stock_ai < backup_20240815_120000.sql
```

#### 配置备份
```bash
# 备份配置文件
cp .env .env.backup
cp -r logs logs_backup
```

## 开发指南

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/test_agents.py

# 运行覆盖率测试
pytest --cov=stock_ai
```

### 代码格式化

```bash
# 格式化代码
black src/ tests/
isort src/ tests/

# 类型检查
mypy src/
```

### 添加新的智能体

1. **创建智能体文件**
   ```bash
   # 在 src/stock_ai/agents/ 目录下创建新文件
   touch src/stock_ai/agents/sentiment_analyzer.py
   ```

2. **实现智能体类**
   ```python
   from .base_agent import BaseAgent

   class SentimentAnalyzer(BaseAgent):
       def __init__(self, name: str = "SentimentAnalyzer"):
           super().__init__(name)

       async def analyze(self, data: dict) -> dict:
           # 实现情感分析逻辑
           pass

       async def make_decision(self, analysis: dict) -> dict:
           # 实现决策逻辑
           pass
   ```

3. **注册智能体**
   在 `src/stock_ai/main.py` 中添加：
   ```python
   from .agents.sentiment_analyzer import SentimentAnalyzer

   # 在智能体初始化部分添加
   sentiment_analyzer = SentimentAnalyzer()
   ```

### 扩展数据源

1. **创建数据提供者**
   ```python
   # src/stock_ai/data/providers/custom_provider.py
   from .base_provider import BaseDataProvider

   class CustomDataProvider(BaseDataProvider):
       async def get_market_data(self, symbol: str) -> dict:
           # 实现数据获取逻辑
           pass
   ```

2. **配置数据源**
   在 `.env` 文件中添加：
   ```bash
   MARKET_DATA_PROVIDER=custom
   CUSTOM_API_KEY=your_api_key
   ```

### 自定义策略

1. **创建策略文件**
   ```python
   # src/stock_ai/strategies/momentum_strategy.py
   from .base_strategy import BaseStrategy

   class MomentumStrategy(BaseStrategy):
       def calculate_signals(self, data: dict) -> dict:
           # 实现动量策略逻辑
           pass
   ```

2. **集成到智能体**
   ```python
   # 在智能体中使用策略
   from ..strategies.momentum_strategy import MomentumStrategy

   class TradingAgent(BaseAgent):
       def __init__(self):
           super().__init__()
           self.strategy = MomentumStrategy()
   ```

## 许可证

MIT License
