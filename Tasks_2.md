[ ] NAME:Current Task List DESCRIPTION:Root task for conversation 0b51537d-de26-419d-8cc3-b02f5defca3e
-[ ] NAME:检查代码重复，做出重构 DESCRIPTION:在src/stock_ai/目录下，存在 database.py 模块 和 database 目录，二者是否可以合并；请做出重构；
另外stock-ai test-db命令会出错，请修复：$ stock-ai test-db
Testing database connection...
✗ Database test failed: cannot import name 'db_manager' from 'stock_ai.database' (/Users/<USER>/workspace/stock_ai/src/stock_ai/database/__init__.py)
-[ ] NAME:丰富智能体工具 DESCRIPTION:请进一步丰富智能体的工具箱，使其投资决策更智能；例如市场研报相关，从主流资讯网站获取市场信息，以便投资决策更优
-[ ] NAME:输出一份本工程开发指南 DESCRIPTION:尽量详细介绍本代码工程的架构，以便开发者对特定功能进行入门和扩展开发
-[ ] NAME:输出一份路线规划 DESCRIPTION:对于本系统的未来规划，输出一份详尽的路线规划，目标是最大化投资收益的改进策略、向最终用户呈现本系统运行过程和投资日报、投资决策、风险报告等；不限于以上主题