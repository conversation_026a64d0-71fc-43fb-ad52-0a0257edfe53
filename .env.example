# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/stock_ai
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=stock_ai
DATABASE_USER=username
DATABASE_PASSWORD=password

# Trading Configuration
TRADING_MODE=SIMULATION  # SIMULATION or LIVE
INITIAL_CAPITAL=100000.0
MAX_POSITION_SIZE=0.1  # Maximum 10% of portfolio per position
STOP_LOSS_PERCENTAGE=0.05  # 5% stop loss
TAKE_PROFIT_PERCENTAGE=0.15  # 15% take profit

# Market Data Configuration
MARKET_DATA_PROVIDER=yahoo  # yahoo, alpha_vantage, etc.
ALPHA_VANTAGE_API_KEY=your_alpha_vantage_key_here
UPDATE_INTERVAL=300  # seconds between market data updates

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/stock_ai.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# Agent Configuration
AGENT_UPDATE_INTERVAL=60  # seconds between agent updates
DECISION_COOLDOWN=300  # minimum seconds between trading decisions
MAX_CONCURRENT_AGENTS=5

# Risk Management
MAX_DAILY_LOSS=0.02  # Maximum 2% daily loss
MAX_DRAWDOWN=0.1  # Maximum 10% drawdown
RISK_FREE_RATE=0.03  # 3% annual risk-free rate

# Market Hours (UTC)
MARKET_OPEN_HOUR=14  # 9:30 AM EST = 14:30 UTC
MARKET_OPEN_MINUTE=30
MARKET_CLOSE_HOUR=21  # 4:00 PM EST = 21:00 UTC
MARKET_CLOSE_MINUTE=0

# Notification Configuration
ENABLE_NOTIFICATIONS=true
NOTIFICATION_WEBHOOK_URL=your_webhook_url_here
NOTIFICATION_LEVEL=WARNING  # DEBUG, INFO, WARNING, ERROR

# Development Configuration
DEBUG=false
TESTING=false
MOCK_MARKET_DATA=false
