"""
Logging configuration for Stock AI Agents system
"""

import sys
from pathlib import Path
from loguru import logger

from ..config import settings


def setup_logging(debug: bool = False) -> None:
    """Setup logging configuration"""
    
    # Remove default logger
    logger.remove()
    
    # Determine log level
    log_level = "DEBUG" if debug else settings.logging.level
    
    # Console logging with colors
    logger.add(
        sys.stderr,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
               "<level>{level: <8}</level> | "
               "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
               "<level>{message}</level>",
        level=log_level,
        colorize=True,
        backtrace=True,
        diagnose=True,
    )
    
    # File logging
    log_file = Path(settings.logging.file)
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    logger.add(
        log_file,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        level=log_level,
        rotation=settings.logging.max_size,
        retention=settings.logging.backup_count,
        compression="zip",
        backtrace=True,
        diagnose=True,
    )
    
    # Set specific loggers
    logger.add(
        log_file.parent / "agents.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        level=log_level,
        filter=lambda record: "agents" in record["name"],
        rotation="10 MB",
        retention=5,
        compression="zip",
    )
    
    logger.add(
        log_file.parent / "trading.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        level=log_level,
        filter=lambda record: any(keyword in record["name"] for keyword in ["trading", "decision", "transaction"]),
        rotation="10 MB",
        retention=10,
        compression="zip",
    )
    
    logger.add(
        log_file.parent / "risk.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}",
        level=log_level,
        filter=lambda record: "risk" in record["name"],
        rotation="5 MB",
        retention=10,
        compression="zip",
    )
    
    logger.info(f"Logging initialized - Level: {log_level}, File: {log_file}")


def get_logger(name: str):
    """Get logger with specific name"""
    return logger.bind(name=name)
