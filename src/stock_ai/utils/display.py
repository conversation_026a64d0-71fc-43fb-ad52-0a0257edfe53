"""
Display utilities for Stock AI Agents system
"""

from datetime import datetime
from typing import Optional

from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.layout import Layout
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.align import Align


def create_status_display(agent_manager, coordination_service) -> Layout:
    """Create live status display"""
    
    layout = Layout()
    
    layout.split_column(
        Layout(name="header", size=3),
        Layout(name="main"),
        Layout(name="footer", size=3),
    )
    
    layout["main"].split_row(
        Layout(name="agents", ratio=2),
        Layout(name="coordination", ratio=1),
    )
    
    # Header
    header_text = Text("🤖 Stock AI Agents System", style="bold blue")
    header_text.append(f" | {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", style="dim")
    layout["header"].update(Panel(Align.center(header_text), style="blue"))
    
    # Agents status
    agents_table = create_agents_table(agent_manager)
    layout["agents"].update(Panel(agents_table, title="Agents Status", border_style="green"))
    
    # Coordination status
    coordination_panel = create_coordination_panel(coordination_service)
    layout["coordination"].update(coordination_panel)
    
    # Footer
    footer_text = Text("Press Ctrl+C to stop", style="dim")
    layout["footer"].update(Panel(Align.center(footer_text), style="dim"))
    
    return layout


def create_agents_table(agent_manager) -> Table:
    """Create agents status table"""
    table = Table(show_header=True, header_style="bold magenta")
    table.add_column("Agent", style="cyan", width=20)
    table.add_column("Status", width=10)
    table.add_column("Executions", justify="right", width=10)
    table.add_column("Errors", justify="right", width=8)
    table.add_column("Last Execution", width=20)
    
    if not agent_manager:
        table.add_row("No agent manager", "❌", "0", "0", "Never")
        return table
    
    try:
        status = agent_manager.get_agent_status()
        agents = status.get("agents", {})
        
        for agent_name, agent_status in agents.items():
            # Status emoji
            status_emoji = {
                "ACTIVE": "🟢",
                "INACTIVE": "⚫",
                "PAUSED": "🟡",
                "ERROR": "🔴"
            }.get(agent_status.get("status", "UNKNOWN"), "❓")
            
            # Format last execution
            last_exec = agent_status.get("last_execution")
            if last_exec:
                try:
                    last_exec_dt = datetime.fromisoformat(last_exec.replace('Z', '+00:00'))
                    last_exec_str = last_exec_dt.strftime("%H:%M:%S")
                except:
                    last_exec_str = "Invalid"
            else:
                last_exec_str = "Never"
            
            table.add_row(
                agent_name.replace("_", " ").title(),
                f"{status_emoji} {agent_status.get('status', 'UNKNOWN')}",
                str(agent_status.get("execution_count", 0)),
                str(agent_status.get("error_count", 0)),
                last_exec_str
            )
    
    except Exception as e:
        table.add_row("Error loading agents", "❌", "0", "0", str(e))
    
    return table


def create_coordination_panel(coordination_service) -> Panel:
    """Create coordination status panel"""
    
    if not coordination_service:
        content = Text("Coordination service not available", style="red")
        return Panel(content, title="Coordination", border_style="red")
    
    try:
        status = coordination_service.get_coordination_status()
        
        content = Text()
        
        # Status
        if status.get("is_running", False):
            content.append("Status: ", style="bold")
            content.append("🟢 Running\n", style="green")
        else:
            content.append("Status: ", style="bold")
            content.append("🔴 Stopped\n", style="red")
        
        # Mode
        content.append("Mode: ", style="bold")
        content.append(f"{status.get('coordination_mode', 'UNKNOWN')}\n")
        
        # Interval
        interval = status.get("coordination_interval", 0)
        content.append("Interval: ", style="bold")
        content.append(f"{interval}s\n")
        
        # Last coordination
        last_coord = status.get("last_coordination")
        if last_coord:
            try:
                last_coord_dt = datetime.fromisoformat(last_coord.replace('Z', '+00:00'))
                last_coord_str = last_coord_dt.strftime("%H:%M:%S")
            except:
                last_coord_str = "Invalid"
        else:
            last_coord_str = "Never"
        
        content.append("Last: ", style="bold")
        content.append(f"{last_coord_str}\n")
        
        # History
        history_len = status.get("coordination_history_length", 0)
        content.append("History: ", style="bold")
        content.append(f"{history_len} events")
        
        return Panel(content, title="Coordination", border_style="blue")
    
    except Exception as e:
        content = Text(f"Error: {str(e)}", style="red")
        return Panel(content, title="Coordination", border_style="red")


def create_portfolio_summary() -> Panel:
    """Create portfolio summary panel"""
    # This would show portfolio metrics
    content = Text("Portfolio Summary\n", style="bold")
    content.append("Value: $100,000\n")
    content.append("Return: +5.2%\n", style="green")
    content.append("Positions: 8\n")
    content.append("Cash: 12.5%")
    
    return Panel(content, title="Portfolio", border_style="yellow")


def create_recent_activity_panel() -> Panel:
    """Create recent activity panel"""
    content = Text("Recent Activity\n", style="bold")
    content.append("• Market analysis completed\n", style="dim")
    content.append("• Risk assessment: GREEN\n", style="green")
    content.append("• 2 new stock candidates\n", style="blue")
    content.append("• Portfolio rebalanced\n", style="yellow")
    
    return Panel(content, title="Activity", border_style="cyan")


def format_currency(amount: float) -> str:
    """Format currency amount"""
    if amount >= 1_000_000:
        return f"${amount/1_000_000:.1f}M"
    elif amount >= 1_000:
        return f"${amount/1_000:.1f}K"
    else:
        return f"${amount:.2f}"


def format_percentage(value: float) -> str:
    """Format percentage value"""
    return f"{value:+.2f}%"


def get_status_color(status: str) -> str:
    """Get color for status"""
    colors = {
        "ACTIVE": "green",
        "INACTIVE": "red",
        "PAUSED": "yellow",
        "ERROR": "red",
        "SUCCESS": "green",
        "WARNING": "yellow",
        "FAILED": "red",
    }
    return colors.get(status.upper(), "white")
