"""
Database connection and session management
"""

import asyncio
from contextlib import asynccontextmanager
from typing import Async<PERSON>enerator, Optional

import asyncpg
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import sessionmaker, Session
from loguru import logger

from ..config import settings


class DatabaseManager:
    """Database connection manager"""
    
    def __init__(self):
        self.sync_engine = None
        self.async_engine = None
        self.sync_session_factory = None
        self.async_session_factory = None
        self._initialized = False
    
    def initialize(self):
        """Initialize database connections"""
        if self._initialized:
            return
        
        # Sync engine for migrations and admin tasks
        # Use psycopg2 for sync operations, but we'll skip sync engine for now
        # since we only have asyncpg installed
        self.sync_engine = None
        
        # Async engine for application use
        async_url = settings.database.url.replace("postgresql://", "postgresql+asyncpg://")
        self.async_engine = create_async_engine(
            async_url,
            echo=settings.debug,
            pool_size=5,
            max_overflow=10,
            pool_pre_ping=True,
        )
        
        # Session factories
        self.sync_session_factory = None  # No sync sessions for now
        
        self.async_session_factory = async_sessionmaker(
            bind=self.async_engine,
            class_=AsyncSession,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False,
        )
        
        self._initialized = True
        logger.info("Database connections initialized")
    
    def get_sync_session(self) -> Session:
        """Get synchronous database session"""
        raise NotImplementedError("Sync sessions not available - use async sessions instead")
    
    def get_async_session(self) -> AsyncSession:
        """Get asynchronous database session"""
        if not self._initialized:
            self.initialize()
        return self.async_session_factory()
    
    @asynccontextmanager
    async def async_session_scope(self) -> AsyncGenerator[AsyncSession, None]:
        """Async context manager for database sessions"""
        if not self._initialized:
            self.initialize()
        
        session = self.async_session_factory()
        try:
            yield session
            await session.commit()
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
    
    async def test_connection(self) -> bool:
        """Test database connection"""
        try:
            if not self._initialized:
                self.initialize()

            from sqlalchemy import text
            async with self.async_session_scope() as session:
                result = await session.execute(text("SELECT 1"))
                return result.scalar() == 1
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    async def close(self):
        """Close database connections"""
        if self.async_engine:
            await self.async_engine.dispose()

        self._initialized = False
        logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()


async def get_db_session() -> AsyncGenerator[AsyncSession, None]:
    """Dependency for getting database session"""
    async with db_manager.async_session_scope() as session:
        yield session


async def create_database_if_not_exists():
    """Create database if it doesn't exist"""
    try:
        # Connect to postgres database to create our database
        conn = await asyncpg.connect(
            host=settings.database.host,
            port=settings.database.port,
            user=settings.database.user,
            password=settings.database.password,
            database="postgres"
        )

        # Check if database exists
        result = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1",
            settings.database.name
        )

        if not result:
            await conn.execute(f'CREATE DATABASE "{settings.database.name}"')
            logger.info(f"Created database: {settings.database.name}")
        else:
            logger.info(f"Database already exists: {settings.database.name}")

        await conn.close()

    except Exception as e:
        logger.error(f"Failed to create database: {e}")
        raise


async def create_tables_async() -> None:
    """Create all database tables (async version)"""
    try:
        if not db_manager._initialized:
            db_manager.initialize()

        # Import all models to ensure they're registered
        from ..models.base import Base
        from .. import models  # noqa: F401 - needed to register models

        async with db_manager.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

        logger.info("Database tables created successfully (async)")

    except Exception as e:
        logger.error(f"Failed to create database tables (async): {e}")
        raise


async def drop_tables_async() -> None:
    """Drop all database tables (async version - use with caution!)"""
    try:
        if not db_manager._initialized:
            db_manager.initialize()

        # Import all models to ensure they're registered
        from ..models.base import Base
        from .. import models  # noqa: F401 - needed to register models

        async with db_manager.async_engine.begin() as conn:
            await conn.run_sync(Base.metadata.drop_all)

        logger.warning("All database tables dropped (async)")

    except Exception as e:
        logger.error(f"Failed to drop database tables (async): {e}")
        raise
