"""
Database migration and initialization utilities
"""

from datetime import datetime
from decimal import Decimal
from typing import List, Dict, Any

from sqlalchemy import text
from loguru import logger

from ..models import Base, Account, Agent, Stock, TradingSession
from ..models.agent import AgentType, AgentStatus
from ..models.market import MarketStatus
from .connection import db_manager


async def create_tables():
    """Create all database tables"""
    try:
        # Use the async version from connection module
        from .connection import create_tables_async
        await create_tables_async()
        logger.info("Database tables created successfully")
    except Exception as e:
        logger.error(f"Failed to create tables: {e}")
        raise


async def drop_tables():
    """Drop all database tables"""
    try:
        # Use the async version from connection module
        from .connection import drop_tables_async
        await drop_tables_async()
        logger.info("Database tables dropped successfully")
    except Exception as e:
        logger.error(f"Failed to drop tables: {e}")
        raise


async def init_database():
    """Initialize database with default data"""
    try:
        await create_tables()
        await _create_default_account()
        await _create_default_agents()
        await _create_sample_stocks()
        await _create_current_trading_session()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
        raise


async def _create_default_account():
    """Create default trading account"""
    from .connection import get_db_session
    from sqlalchemy import select

    async with get_db_session() as session:
        try:
            # Check if default account exists
            result = await session.execute(select(Account).filter_by(name="default"))
            existing_account = result.scalar_one_or_none()
            if existing_account:
                logger.info("Default account already exists")
                return

            # Create default account for Chinese A-share trading
            account = Account(
                name="default",
                account_type="SIMULATION",
                initial_capital=Decimal("1000000.00"),  # 100万人民币
                current_cash=Decimal("1000000.00"),
                total_value=Decimal("1000000.00"),
                max_position_size=Decimal("0.1"),
                stop_loss_percentage=Decimal("0.05"),
                take_profit_percentage=Decimal("0.15"),
            )

            session.add(account)
            await session.commit()
            logger.info("Created default account with ¥1,000,000 initial capital for A-share trading")

        except Exception as e:
            await session.rollback()
            logger.error(f"Failed to create default account: {e}")
            raise


async def _create_default_agents():
    """Create default agents"""
    from .connection import get_db_session
    from sqlalchemy import select

    async with get_db_session() as session:
        try:
            # Agent configurations
            agents_config = [
            {
                "name": "market_analyst",
                "agent_type": AgentType.MARKET_ANALYST,
                "description": "Analyzes market trends and economic indicators",
                "system_prompt": """You are a Market Analyst AI agent specializing in stock market analysis.

Your responsibilities:
1. Analyze market trends and patterns
2. Monitor economic indicators and news
3. Assess market sentiment and volatility
4. Identify potential market opportunities and risks
5. Provide market context for other agents' decisions

You have access to market data, news feeds, and economic indicators. Use this information to provide comprehensive market analysis and insights.

Always provide data-driven analysis with clear reasoning and confidence levels.""",
                "tools_config": ["market_data_tool", "news_analysis_tool", "economic_indicators_tool"]
            },
            {
                "name": "stock_selector",
                "agent_type": AgentType.STOCK_SELECTOR,
                "description": "Selects stocks based on technical and fundamental analysis",
                "system_prompt": """You are a Stock Selector AI agent specializing in stock selection and analysis.

Your responsibilities:
1. Perform technical analysis on stocks
2. Conduct fundamental analysis of companies
3. Screen stocks based on various criteria
4. Maintain and update candidate stock pools
5. Evaluate stock investment potential and risks

You have access to stock data, financial statements, and technical indicators. Use comprehensive analysis to identify promising investment opportunities.

Always provide detailed analysis with clear buy/sell/hold recommendations and risk assessments.""",
                "tools_config": ["stock_screener_tool", "technical_analysis_tool", "market_research_tool", "news_analysis_tool"]
            },
            {
                "name": "trading_decision_maker",
                "agent_type": AgentType.TRADING_DECISION_MAKER,
                "description": "Makes specific buy/sell trading decisions",
                "system_prompt": """You are a Trading Decision Maker AI agent responsible for executing trading decisions.

Your responsibilities:
1. Make specific buy/sell/hold decisions
2. Determine optimal entry and exit points
3. Set stop-loss and take-profit levels
4. Manage position sizing and risk
5. Execute trades based on analysis from other agents

You must record detailed reasoning for every trading decision. Consider market conditions, technical indicators, and risk management principles.

Always provide clear decision rationale with specific price targets and risk parameters.""",
                "tools_config": ["trading_tool", "position_sizing_tool", "risk_management_tool", "decision_recorder_tool"]
            },
            {
                "name": "portfolio_manager",
                "agent_type": AgentType.PORTFOLIO_MANAGER,
                "description": "Manages overall portfolio allocation and performance",
                "system_prompt": """You are a Portfolio Manager AI agent responsible for overall portfolio management.

Your responsibilities:
1. Optimize asset allocation across positions
2. Monitor portfolio performance and risk metrics
3. Rebalance portfolio when necessary
4. Ensure diversification and risk limits
5. Track portfolio against benchmarks and goals

You have access to portfolio data, performance metrics, and risk analytics. Use this information to maintain an optimal portfolio structure.

Always consider risk-adjusted returns and maintain proper diversification.""",
                "tools_config": ["portfolio_analysis_tool", "rebalancing_tool", "performance_tracking_tool"]
            },
            {
                "name": "risk_controller",
                "agent_type": AgentType.RISK_CONTROLLER,
                "description": "Monitors and controls investment risks",
                "system_prompt": """You are a Risk Controller AI agent responsible for risk management and control.

Your responsibilities:
1. Monitor portfolio and position-level risks
2. Enforce risk limits and stop-loss rules
3. Provide risk warnings and alerts
4. Assess correlation and concentration risks
5. Implement risk mitigation strategies

You have access to risk metrics, volatility data, and correlation analysis. Use this information to protect the portfolio from excessive losses.

Always prioritize capital preservation and risk-adjusted returns over absolute returns.""",
                "tools_config": ["risk_analysis_tool", "volatility_tool", "correlation_tool", "alert_tool"]
            }
        ]
        
            for agent_config in agents_config:
                # Check if agent already exists
                result = await session.execute(select(Agent).filter_by(name=agent_config["name"]))
                existing_agent = result.scalar_one_or_none()
                if existing_agent:
                    logger.info(f"Agent {agent_config['name']} already exists")
                    continue

                # Create agent
                agent = Agent(
                    name=agent_config["name"],
                    agent_type=agent_config["agent_type"],
                    status=AgentStatus.ACTIVE,
                    description=agent_config["description"],
                    system_prompt=agent_config["system_prompt"],
                    model_name="gpt-4",
                    temperature="0.7",
                    max_tokens=2000,
                    tools_config=agent_config["tools_config"],
                )

                session.add(agent)
                logger.info(f"Created agent: {agent_config['name']}")

            await session.commit()
            logger.info("Created default agents")

        except Exception as e:
            await session.rollback()
            logger.error(f"Failed to create default agents: {e}")
            raise


async def _create_sample_stocks():
    """Create sample stocks for testing"""
    from .connection import get_db_session
    from sqlalchemy import select

    async with get_db_session() as session:
        try:
            # Sample Chinese A-share stocks
            sample_stocks = [
            {"symbol": "000001.SZ", "name": "平安银行", "exchange": "SZSE", "sector": "金融", "industry": "银行"},
            {"symbol": "000002.SZ", "name": "万科A", "exchange": "SZSE", "sector": "房地产", "industry": "房地产开发"},
            {"symbol": "000858.SZ", "name": "五粮液", "exchange": "SZSE", "sector": "消费", "industry": "白酒"},
            {"symbol": "600036.SH", "name": "招商银行", "exchange": "SSE", "sector": "金融", "industry": "银行"},
            {"symbol": "600519.SH", "name": "贵州茅台", "exchange": "SSE", "sector": "消费", "industry": "白酒"},
            {"symbol": "600887.SH", "name": "伊利股份", "exchange": "SSE", "sector": "消费", "industry": "乳制品"},
            {"symbol": "002415.SZ", "name": "海康威视", "exchange": "SZSE", "sector": "科技", "industry": "安防设备"},
            {"symbol": "300059.SZ", "name": "东方财富", "exchange": "SZSE", "sector": "金融", "industry": "证券"},
            {"symbol": "300750.SZ", "name": "宁德时代", "exchange": "SZSE", "sector": "新能源", "industry": "电池"},
        ]
        
            for stock_data in sample_stocks:
                # Check if stock already exists
                result = await session.execute(select(Stock).filter_by(symbol=stock_data["symbol"]))
                existing_stock = result.scalar_one_or_none()
                if existing_stock:
                    continue

                # Create stock
                stock = Stock(
                    symbol=stock_data["symbol"],
                    name=stock_data["name"],
                    exchange=stock_data["exchange"],
                    sector=stock_data["sector"],
                    industry=stock_data["industry"],
                    currency="CNY",
                    is_active=True,
                    is_tradable=True,
                )

                session.add(stock)

            await session.commit()
            logger.info("Created sample stocks")

        except Exception as e:
            await session.rollback()
            logger.error(f"Failed to create sample stocks: {e}")
            raise


async def _create_current_trading_session():
    """Create current trading session"""
    from .connection import get_db_session
    from sqlalchemy import select

    async with get_db_session() as session:
        try:
            today = datetime.now().date()

            # Check if today's session exists
            result = await session.execute(
                select(TradingSession).filter(
                    TradingSession.date >= datetime.combine(today, datetime.min.time()),
                    TradingSession.date < datetime.combine(today, datetime.max.time())
                )
            )
            existing_session = result.scalar_one_or_none()

            if existing_session:
                logger.info("Today's trading session already exists")
                return

            # Create today's trading session
            trading_session = TradingSession(
                date=datetime.combine(today, datetime.min.time()),
                market_status=MarketStatus.CLOSED,  # Will be updated by market data service
                market_open=datetime.combine(today, datetime.min.time().replace(hour=14, minute=30)),  # 9:30 AM EST
                market_close=datetime.combine(today, datetime.min.time().replace(hour=21, minute=0)),   # 4:00 PM EST
            )

            session.add(trading_session)
            await session.commit()
            logger.info("Created current trading session")

        except Exception as e:
            await session.rollback()
            logger.error(f"Failed to create trading session: {e}")
            raise
