"""
Decision recording and plan management tools for Stock AI Agents system
"""

import os
from decimal import Decimal
from typing import Dict, List, Optional, Any
from datetime import datetime
from pathlib import Path

from sqlalchemy import select, and_, desc

from .base import BaseTool, ToolResult, ToolStatus, ToolDefinition, ToolParameter, ToolError
from ..database import get_db_session
from ..models import Decision, Plan, PlanUpdate, Agent, Account
from ..models.decision import DecisionType, DecisionStatus, PlanStatus


class DecisionRecorderTool(BaseTool):
    """Tool for recording and managing trading decisions"""
    
    def __init__(self):
        super().__init__(
            name="decision_recorder_tool",
            description="Record, track, and manage trading decisions with detailed reasoning"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="action",
                    type="string",
                    description="Action to perform",
                    enum=["record_decision", "get_decisions", "update_decision", "approve_decision"]
                ),
                ToolParameter(
                    name="decision_data",
                    type="object",
                    description="Decision data (required for record_decision)",
                    required=False
                ),
                ToolParameter(
                    name="decision_id",
                    type="number",
                    description="Decision ID (for update/approve actions)",
                    required=False
                ),
                ToolParameter(
                    name="agent_name",
                    type="string",
                    description="Agent name",
                    default="trading_decision_maker"
                ),
                ToolParameter(
                    name="account_name",
                    type="string",
                    description="Account name",
                    default="default"
                ),
                ToolParameter(
                    name="limit",
                    type="number",
                    description="Maximum number of decisions to retrieve",
                    default=20,
                    minimum=1,
                    maximum=100
                ),
            ]
        )
    
    async def execute(self, action: str, decision_data: Dict = None, decision_id: int = None,
                     agent_name: str = "trading_decision_maker", account_name: str = "default",
                     limit: int = 20) -> ToolResult:
        """Execute decision recording action"""
        
        async for session in get_db_session():
            try:
                if action == "record_decision":
                    if not decision_data:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Decision data is required for record_decision action"
                        )
                    return await self._record_decision(session, decision_data, agent_name, account_name)
                elif action == "get_decisions":
                    return await self._get_decisions(session, agent_name, account_name, limit)
                elif action == "update_decision":
                    if not decision_id:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Decision ID is required for update_decision action"
                        )
                    return await self._update_decision(session, decision_id, decision_data or {})
                elif action == "approve_decision":
                    if not decision_id:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Decision ID is required for approve_decision action"
                        )
                    return await self._approve_decision(session, decision_id)
                else:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Unknown action: {action}"
                    )
                    
            except Exception as e:
                await session.rollback()
                raise ToolError(f"Decision recorder tool execution failed: {str(e)}")
    
    async def _record_decision(self, session, decision_data: Dict, agent_name: str, account_name: str) -> ToolResult:
        """Record a new trading decision"""
        # Get agent and account
        agent_result = await session.execute(
            select(Agent).where(Agent.name == agent_name)
        )
        agent = agent_result.scalar_one_or_none()
        
        account_result = await session.execute(
            select(Account).where(Account.name == account_name)
        )
        account = account_result.scalar_one_or_none()
        
        if not agent:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Agent '{agent_name}' not found"
            )
        
        if not account:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Account '{account_name}' not found"
            )
        
        # Validate required fields
        required_fields = ["decision_type", "stock_symbol", "reasoning"]
        for field in required_fields:
            if field not in decision_data:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Required field '{field}' is missing from decision data"
                )
        
        # Create decision
        decision = Decision(
            agent_id=agent.id,
            account_id=account.id,
            decision_type=DecisionType(decision_data["decision_type"]),
            status=DecisionStatus.PENDING,
            stock_symbol=decision_data["stock_symbol"].upper(),
            quantity=Decimal(str(decision_data["quantity"])) if decision_data.get("quantity") else None,
            target_price=Decimal(str(decision_data["target_price"])) if decision_data.get("target_price") else None,
            stop_loss_price=Decimal(str(decision_data["stop_loss_price"])) if decision_data.get("stop_loss_price") else None,
            take_profit_price=Decimal(str(decision_data["take_profit_price"])) if decision_data.get("take_profit_price") else None,
            reasoning=decision_data["reasoning"],
            confidence_score=Decimal(str(decision_data["confidence_score"])) if decision_data.get("confidence_score") else None,
            risk_assessment=decision_data.get("risk_assessment"),
            market_price_at_decision=Decimal(str(decision_data["market_price_at_decision"])) if decision_data.get("market_price_at_decision") else None,
            market_conditions=decision_data.get("market_conditions"),
            technical_indicators=decision_data.get("technical_indicators"),
        )
        
        session.add(decision)
        await session.commit()
        await session.refresh(decision)
        
        # Save decision to markdown file
        decision_file_path = await self._save_decision_to_file(decision, decision_data)
        if decision_file_path:
            decision.decision_file_path = decision_file_path
            await session.commit()
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data={
                "decision_id": decision.id,
                "decision_file_path": decision_file_path
            },
            message=f"Recorded {decision.decision_type.value} decision for {decision.stock_symbol}"
        )
    
    async def _get_decisions(self, session, agent_name: str, account_name: str, limit: int) -> ToolResult:
        """Get recent decisions"""
        # Get agent and account
        agent_result = await session.execute(
            select(Agent).where(Agent.name == agent_name)
        )
        agent = agent_result.scalar_one_or_none()
        
        account_result = await session.execute(
            select(Account).where(Account.name == account_name)
        )
        account = account_result.scalar_one_or_none()
        
        query = select(Decision).order_by(desc(Decision.created_at)).limit(limit)
        
        if agent:
            query = query.where(Decision.agent_id == agent.id)
        if account:
            query = query.where(Decision.account_id == account.id)
        
        result = await session.execute(query)
        decisions = result.scalars().all()
        
        decisions_data = []
        for decision in decisions:
            decision_data = {
                "id": decision.id,
                "decision_type": decision.decision_type.value,
                "status": decision.status.value,
                "stock_symbol": decision.stock_symbol,
                "quantity": float(decision.quantity) if decision.quantity else None,
                "target_price": float(decision.target_price) if decision.target_price else None,
                "stop_loss_price": float(decision.stop_loss_price) if decision.stop_loss_price else None,
                "take_profit_price": float(decision.take_profit_price) if decision.take_profit_price else None,
                "reasoning": decision.reasoning,
                "confidence_score": float(decision.confidence_score) if decision.confidence_score else None,
                "risk_assessment": decision.risk_assessment,
                "market_price_at_decision": float(decision.market_price_at_decision) if decision.market_price_at_decision else None,
                "created_at": decision.created_at.isoformat(),
                "approved_at": decision.approved_at.isoformat() if decision.approved_at else None,
                "executed_at": decision.executed_at.isoformat() if decision.executed_at else None,
                "decision_file_path": decision.decision_file_path,
            }
            decisions_data.append(decision_data)
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=decisions_data,
            message=f"Retrieved {len(decisions_data)} decisions"
        )
    
    async def _update_decision(self, session, decision_id: int, update_data: Dict) -> ToolResult:
        """Update decision"""
        result = await session.execute(
            select(Decision).where(Decision.id == decision_id)
        )
        decision = result.scalar_one_or_none()
        
        if not decision:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Decision with ID {decision_id} not found"
            )
        
        allowed_fields = [
            "status", "target_price", "stop_loss_price", "take_profit_price",
            "reasoning", "confidence_score", "risk_assessment", "execution_notes"
        ]
        updated_fields = []
        
        for field, value in update_data.items():
            if field in allowed_fields and hasattr(decision, field):
                if field == "status":
                    setattr(decision, field, DecisionStatus(value))
                elif field in ["target_price", "stop_loss_price", "take_profit_price", "confidence_score"]:
                    setattr(decision, field, Decimal(str(value)) if value is not None else None)
                else:
                    setattr(decision, field, value)
                updated_fields.append(field)
        
        if updated_fields:
            await session.commit()
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data={"updated_fields": updated_fields},
                message=f"Updated decision {decision_id}: {', '.join(updated_fields)}"
            )
        else:
            return ToolResult(
                status=ToolStatus.WARNING,
                message="No valid fields to update"
            )
    
    async def _approve_decision(self, session, decision_id: int) -> ToolResult:
        """Approve a pending decision"""
        result = await session.execute(
            select(Decision).where(Decision.id == decision_id)
        )
        decision = result.scalar_one_or_none()
        
        if not decision:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Decision with ID {decision_id} not found"
            )
        
        if decision.status != DecisionStatus.PENDING:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Decision {decision_id} is not in PENDING status"
            )
        
        decision.status = DecisionStatus.APPROVED
        decision.approved_at = datetime.now()
        await session.commit()
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            message=f"Approved decision {decision_id} for {decision.decision_type.value} {decision.stock_symbol}"
        )
    
    async def _save_decision_to_file(self, decision: Decision, decision_data: Dict) -> Optional[str]:
        """Save decision to markdown file"""
        try:
            # Create decisions directory if it doesn't exist
            decisions_dir = Path("data/decisions")
            decisions_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{decision.decision_type.value.lower()}_{decision.stock_symbol.lower()}.md"
            file_path = decisions_dir / filename
            
            # Create markdown content
            content = f"""# Trading Decision: {decision.decision_type.value} {decision.stock_symbol}

**Decision ID:** {decision.id}
**Date:** {decision.created_at.strftime('%Y-%m-%d %H:%M:%S')}
**Status:** {decision.status.value}

## Decision Details

- **Stock Symbol:** {decision.stock_symbol}
- **Decision Type:** {decision.decision_type.value}
- **Quantity:** {decision.quantity if decision.quantity else 'N/A'}
- **Target Price:** ${decision.target_price if decision.target_price else 'N/A'}
- **Stop Loss:** ${decision.stop_loss_price if decision.stop_loss_price else 'N/A'}
- **Take Profit:** ${decision.take_profit_price if decision.take_profit_price else 'N/A'}
- **Confidence Score:** {decision.confidence_score if decision.confidence_score else 'N/A'}

## Market Context

- **Market Price at Decision:** ${decision.market_price_at_decision if decision.market_price_at_decision else 'N/A'}

## Reasoning

{decision.reasoning}

## Risk Assessment

{decision.risk_assessment if decision.risk_assessment else 'No specific risk assessment provided.'}

## Technical Indicators

{decision_data.get('technical_indicators', 'No technical indicators provided.')}

## Market Conditions

{decision_data.get('market_conditions', 'No market conditions provided.')}

---
*Generated by Stock AI Agents System*
"""
            
            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return str(file_path)
            
        except Exception as e:
            # Log error but don't fail the decision recording
            print(f"Failed to save decision to file: {e}")
            return None


class PlanManagerTool(BaseTool):
    """Tool for managing investment plans"""
    
    def __init__(self):
        super().__init__(
            name="plan_manager_tool",
            description="Create, update, and manage dynamic investment plans"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="action",
                    type="string",
                    description="Action to perform",
                    enum=["create_plan", "get_plans", "update_plan", "add_plan_update", "get_plan_updates"]
                ),
                ToolParameter(
                    name="plan_data",
                    type="object",
                    description="Plan data (required for create_plan)",
                    required=False
                ),
                ToolParameter(
                    name="plan_id",
                    type="number",
                    description="Plan ID (for plan-specific actions)",
                    required=False
                ),
                ToolParameter(
                    name="update_data",
                    type="object",
                    description="Update data",
                    required=False
                ),
                ToolParameter(
                    name="agent_name",
                    type="string",
                    description="Agent name",
                    default="portfolio_manager"
                ),
                ToolParameter(
                    name="account_name",
                    type="string",
                    description="Account name",
                    default="default"
                ),
            ]
        )
    
    async def execute(self, action: str, plan_data: Dict = None, plan_id: int = None,
                     update_data: Dict = None, agent_name: str = "portfolio_manager",
                     account_name: str = "default") -> ToolResult:
        """Execute plan management action"""
        
        async for session in get_db_session():
            try:
                if action == "create_plan":
                    if not plan_data:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Plan data is required for create_plan action"
                        )
                    return await self._create_plan(session, plan_data, agent_name, account_name)
                elif action == "get_plans":
                    return await self._get_plans(session, agent_name, account_name)
                elif action == "update_plan":
                    if not plan_id:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Plan ID is required for update_plan action"
                        )
                    return await self._update_plan(session, plan_id, update_data or {})
                elif action == "add_plan_update":
                    if not plan_id:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Plan ID is required for add_plan_update action"
                        )
                    return await self._add_plan_update(session, plan_id, update_data or {}, agent_name)
                elif action == "get_plan_updates":
                    if not plan_id:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Plan ID is required for get_plan_updates action"
                        )
                    return await self._get_plan_updates(session, plan_id)
                else:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Unknown action: {action}"
                    )
                    
            except Exception as e:
                await session.rollback()
                raise ToolError(f"Plan manager tool execution failed: {str(e)}")
    
    async def _create_plan(self, session, plan_data: Dict, agent_name: str, account_name: str) -> ToolResult:
        """Create a new investment plan"""
        # Get agent and account
        agent_result = await session.execute(
            select(Agent).where(Agent.name == agent_name)
        )
        agent = agent_result.scalar_one_or_none()
        
        account_result = await session.execute(
            select(Account).where(Account.name == account_name)
        )
        account = account_result.scalar_one_or_none()
        
        if not agent:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Agent '{agent_name}' not found"
            )
        
        if not account:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Account '{account_name}' not found"
            )
        
        # Validate required fields
        required_fields = ["name", "description", "strategy_type"]
        for field in required_fields:
            if field not in plan_data:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Required field '{field}' is missing from plan data"
                )
        
        # Create plan
        plan = Plan(
            agent_id=agent.id,
            account_id=account.id,
            name=plan_data["name"],
            description=plan_data["description"],
            status=PlanStatus.ACTIVE,
            start_date=datetime.fromisoformat(plan_data["start_date"]) if plan_data.get("start_date") else datetime.now(),
            end_date=datetime.fromisoformat(plan_data["end_date"]) if plan_data.get("end_date") else None,
            review_frequency=plan_data.get("review_frequency", "WEEKLY"),
            target_return=Decimal(str(plan_data["target_return"])) if plan_data.get("target_return") else None,
            max_risk=Decimal(str(plan_data["max_risk"])) if plan_data.get("max_risk") else None,
            strategy_type=plan_data["strategy_type"],
            asset_allocation=plan_data.get("asset_allocation"),
            initial_value=Decimal(str(plan_data["initial_value"])) if plan_data.get("initial_value") else account.total_value,
        )
        
        session.add(plan)
        await session.commit()
        await session.refresh(plan)
        
        # Save plan to markdown file
        plan_file_path = await self._save_plan_to_file(plan, plan_data)
        if plan_file_path:
            plan.plan_file_path = plan_file_path
            await session.commit()
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data={
                "plan_id": plan.id,
                "plan_file_path": plan_file_path
            },
            message=f"Created investment plan: {plan.name}"
        )
    
    async def _get_plans(self, session, agent_name: str, account_name: str) -> ToolResult:
        """Get investment plans"""
        # Get agent and account
        agent_result = await session.execute(
            select(Agent).where(Agent.name == agent_name)
        )
        agent = agent_result.scalar_one_or_none()
        
        account_result = await session.execute(
            select(Account).where(Account.name == account_name)
        )
        account = account_result.scalar_one_or_none()
        
        query = select(Plan).order_by(desc(Plan.created_at))
        
        if agent:
            query = query.where(Plan.agent_id == agent.id)
        if account:
            query = query.where(Plan.account_id == account.id)
        
        result = await session.execute(query)
        plans = result.scalars().all()
        
        plans_data = []
        for plan in plans:
            plan_data = {
                "id": plan.id,
                "name": plan.name,
                "description": plan.description,
                "status": plan.status.value,
                "start_date": plan.start_date.isoformat(),
                "end_date": plan.end_date.isoformat() if plan.end_date else None,
                "review_frequency": plan.review_frequency,
                "target_return": float(plan.target_return) if plan.target_return else None,
                "max_risk": float(plan.max_risk) if plan.max_risk else None,
                "strategy_type": plan.strategy_type,
                "asset_allocation": plan.asset_allocation,
                "initial_value": float(plan.initial_value) if plan.initial_value else None,
                "current_value": float(plan.current_value) if plan.current_value else None,
                "realized_return": float(plan.realized_return) if plan.realized_return else None,
                "unrealized_return": float(plan.unrealized_return) if plan.unrealized_return else None,
                "created_at": plan.created_at.isoformat(),
                "plan_file_path": plan.plan_file_path,
            }
            plans_data.append(plan_data)
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=plans_data,
            message=f"Retrieved {len(plans_data)} investment plans"
        )
    
    async def _update_plan(self, session, plan_id: int, update_data: Dict) -> ToolResult:
        """Update investment plan"""
        result = await session.execute(
            select(Plan).where(Plan.id == plan_id)
        )
        plan = result.scalar_one_or_none()
        
        if not plan:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Plan with ID {plan_id} not found"
            )
        
        allowed_fields = [
            "status", "end_date", "review_frequency", "target_return", "max_risk",
            "asset_allocation", "current_value", "realized_return", "unrealized_return"
        ]
        updated_fields = []
        
        for field, value in update_data.items():
            if field in allowed_fields and hasattr(plan, field):
                if field == "status":
                    setattr(plan, field, PlanStatus(value))
                elif field == "end_date":
                    setattr(plan, field, datetime.fromisoformat(value) if value else None)
                elif field in ["target_return", "max_risk", "current_value", "realized_return", "unrealized_return"]:
                    setattr(plan, field, Decimal(str(value)) if value is not None else None)
                else:
                    setattr(plan, field, value)
                updated_fields.append(field)
        
        if updated_fields:
            await session.commit()
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data={"updated_fields": updated_fields},
                message=f"Updated plan {plan_id}: {', '.join(updated_fields)}"
            )
        else:
            return ToolResult(
                status=ToolStatus.WARNING,
                message="No valid fields to update"
            )
    
    async def _add_plan_update(self, session, plan_id: int, update_data: Dict, agent_name: str) -> ToolResult:
        """Add update to investment plan"""
        # Get plan and agent
        plan_result = await session.execute(
            select(Plan).where(Plan.id == plan_id)
        )
        plan = plan_result.scalar_one_or_none()
        
        agent_result = await session.execute(
            select(Agent).where(Agent.name == agent_name)
        )
        agent = agent_result.scalar_one_or_none()
        
        if not plan:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Plan with ID {plan_id} not found"
            )
        
        if not agent:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Agent '{agent_name}' not found"
            )
        
        # Validate required fields
        required_fields = ["update_type", "summary", "details"]
        for field in required_fields:
            if field not in update_data:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Required field '{field}' is missing from update data"
                )
        
        # Create plan update
        plan_update = PlanUpdate(
            plan_id=plan.id,
            agent_id=agent.id,
            update_type=update_data["update_type"],
            summary=update_data["summary"],
            details=update_data["details"],
            changes_made=update_data.get("changes_made"),
            previous_values=update_data.get("previous_values"),
            new_values=update_data.get("new_values"),
            portfolio_value=Decimal(str(update_data["portfolio_value"])) if update_data.get("portfolio_value") else None,
            return_since_start=Decimal(str(update_data["return_since_start"])) if update_data.get("return_since_start") else None,
            return_since_last_update=Decimal(str(update_data["return_since_last_update"])) if update_data.get("return_since_last_update") else None,
        )
        
        session.add(plan_update)
        await session.commit()
        await session.refresh(plan_update)
        
        # Save update to markdown file
        update_file_path = await self._save_plan_update_to_file(plan, plan_update, update_data)
        if update_file_path:
            plan_update.update_file_path = update_file_path
            await session.commit()
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data={
                "update_id": plan_update.id,
                "update_file_path": update_file_path
            },
            message=f"Added update to plan {plan.name}: {update_data['update_type']}"
        )
    
    async def _get_plan_updates(self, session, plan_id: int) -> ToolResult:
        """Get plan updates"""
        result = await session.execute(
            select(PlanUpdate)
            .where(PlanUpdate.plan_id == plan_id)
            .order_by(desc(PlanUpdate.created_at))
        )
        updates = result.scalars().all()
        
        updates_data = []
        for update in updates:
            update_data = {
                "id": update.id,
                "update_type": update.update_type,
                "summary": update.summary,
                "details": update.details,
                "changes_made": update.changes_made,
                "previous_values": update.previous_values,
                "new_values": update.new_values,
                "portfolio_value": float(update.portfolio_value) if update.portfolio_value else None,
                "return_since_start": float(update.return_since_start) if update.return_since_start else None,
                "return_since_last_update": float(update.return_since_last_update) if update.return_since_last_update else None,
                "created_at": update.created_at.isoformat(),
                "update_file_path": update.update_file_path,
            }
            updates_data.append(update_data)
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=updates_data,
            message=f"Retrieved {len(updates_data)} plan updates"
        )
    
    async def _save_plan_to_file(self, plan: Plan, plan_data: Dict) -> Optional[str]:
        """Save plan to markdown file"""
        try:
            # Create plans directory if it doesn't exist
            plans_dir = Path("data/plans")
            plans_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in plan.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_name = safe_name.replace(' ', '_').lower()
            filename = f"{timestamp}_{safe_name}_plan.md"
            file_path = plans_dir / filename
            
            # Create markdown content
            content = f"""# Investment Plan: {plan.name}

**Plan ID:** {plan.id}
**Created:** {plan.created_at.strftime('%Y-%m-%d %H:%M:%S')}
**Status:** {plan.status.value}

## Plan Overview

{plan.description}

## Plan Details

- **Strategy Type:** {plan.strategy_type}
- **Start Date:** {plan.start_date.strftime('%Y-%m-%d')}
- **End Date:** {plan.end_date.strftime('%Y-%m-%d') if plan.end_date else 'Open-ended'}
- **Review Frequency:** {plan.review_frequency}
- **Target Return:** {plan.target_return}% (if specified)
- **Maximum Risk:** {plan.max_risk}% (if specified)
- **Initial Value:** ${plan.initial_value if plan.initial_value else 'N/A'}

## Asset Allocation

{plan.asset_allocation if plan.asset_allocation else 'No specific allocation defined.'}

## Performance Tracking

- **Current Value:** ${plan.current_value if plan.current_value else 'N/A'}
- **Realized Return:** {plan.realized_return}% (if available)
- **Unrealized Return:** {plan.unrealized_return}% (if available)

---
*Generated by Stock AI Agents System*
"""
            
            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return str(file_path)
            
        except Exception as e:
            # Log error but don't fail the plan creation
            print(f"Failed to save plan to file: {e}")
            return None
    
    async def _save_plan_update_to_file(self, plan: Plan, plan_update: PlanUpdate, update_data: Dict) -> Optional[str]:
        """Save plan update to markdown file"""
        try:
            # Create plans directory if it doesn't exist
            plans_dir = Path("data/plans")
            plans_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            safe_name = "".join(c for c in plan.name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_name = safe_name.replace(' ', '_').lower()
            filename = f"{timestamp}_{safe_name}_update_{plan_update.update_type.lower()}.md"
            file_path = plans_dir / filename
            
            # Create markdown content
            content = f"""# Plan Update: {plan.name}

**Update ID:** {plan_update.id}
**Plan ID:** {plan.id}
**Date:** {plan_update.created_at.strftime('%Y-%m-%d %H:%M:%S')}
**Update Type:** {plan_update.update_type}

## Summary

{plan_update.summary}

## Details

{plan_update.details}

## Changes Made

{plan_update.changes_made if plan_update.changes_made else 'No specific changes recorded.'}

## Performance Update

- **Portfolio Value:** ${plan_update.portfolio_value if plan_update.portfolio_value else 'N/A'}
- **Return Since Start:** {plan_update.return_since_start}% (if available)
- **Return Since Last Update:** {plan_update.return_since_last_update}% (if available)

## Previous vs New Values

**Previous Values:**
{plan_update.previous_values if plan_update.previous_values else 'No previous values recorded.'}

**New Values:**
{plan_update.new_values if plan_update.new_values else 'No new values recorded.'}

---
*Generated by Stock AI Agents System*
"""
            
            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            return str(file_path)
            
        except Exception as e:
            # Log error but don't fail the update creation
            print(f"Failed to save plan update to file: {e}")
            return None
