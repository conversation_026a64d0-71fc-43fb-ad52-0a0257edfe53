"""
Notification and alert tools for Stock AI Agents system
"""

import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum

import httpx
from loguru import logger

from .base import BaseTool, ToolResult, ToolStatus, ToolDefinition, ToolParameter, ToolError
from ..config import settings


class AlertLevel(str, Enum):
    """Alert severity levels"""
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class AlertTool(BaseTool):
    """Tool for generating and managing alerts"""
    
    def __init__(self):
        super().__init__(
            name="alert_tool",
            description="Generate alerts for important events, risks, and opportunities"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="alert_type",
                    type="string",
                    description="Type of alert",
                    enum=["price_alert", "risk_alert", "opportunity_alert", "system_alert", "performance_alert"]
                ),
                ToolParameter(
                    name="level",
                    type="string",
                    description="Alert severity level",
                    enum=["INFO", "WARNING", "ERROR", "CRITICAL"],
                    default="INFO"
                ),
                ToolParameter(
                    name="title",
                    type="string",
                    description="Alert title"
                ),
                ToolParameter(
                    name="message",
                    type="string",
                    description="Alert message"
                ),
                ToolParameter(
                    name="data",
                    type="object",
                    description="Additional alert data",
                    required=False
                ),
                ToolParameter(
                    name="symbol",
                    type="string",
                    description="Related stock symbol (if applicable)",
                    required=False
                ),
                ToolParameter(
                    name="agent_name",
                    type="string",
                    description="Name of the agent generating the alert",
                    required=False
                ),
            ]
        )
    
    async def execute(self, alert_type: str, level: str, title: str, message: str,
                     data: Dict = None, symbol: str = None, agent_name: str = None) -> ToolResult:
        """Generate an alert"""
        
        try:
            alert_level = AlertLevel(level)
            
            # Create alert data
            alert = {
                "timestamp": datetime.now().isoformat(),
                "alert_type": alert_type,
                "level": alert_level.value,
                "title": title,
                "message": message,
                "symbol": symbol,
                "agent_name": agent_name,
                "data": data or {},
            }
            
            # Log the alert
            log_message = f"[{alert_level.value}] {title}: {message}"
            if symbol:
                log_message += f" (Symbol: {symbol})"
            if agent_name:
                log_message += f" (Agent: {agent_name})"
            
            if alert_level == AlertLevel.CRITICAL:
                logger.critical(log_message)
            elif alert_level == AlertLevel.ERROR:
                logger.error(log_message)
            elif alert_level == AlertLevel.WARNING:
                logger.warning(log_message)
            else:
                logger.info(log_message)
            
            # Store alert (in production, this would go to a database)
            await self._store_alert(alert)
            
            # Send notifications if enabled and level is high enough
            if settings.notifications.enabled and self._should_notify(alert_level):
                await self._send_notification(alert)
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data=alert,
                message=f"Generated {alert_level.value} alert: {title}"
            )
            
        except Exception as e:
            raise ToolError(f"Failed to generate alert: {str(e)}")
    
    def _should_notify(self, alert_level: AlertLevel) -> bool:
        """Determine if notification should be sent based on level"""
        notification_level = AlertLevel(settings.notifications.level)
        
        level_priority = {
            AlertLevel.INFO: 1,
            AlertLevel.WARNING: 2,
            AlertLevel.ERROR: 3,
            AlertLevel.CRITICAL: 4
        }
        
        return level_priority[alert_level] >= level_priority[notification_level]
    
    async def _store_alert(self, alert: Dict) -> None:
        """Store alert (placeholder - would use database in production)"""
        # In production, this would store to database
        # For now, just log that we would store it
        logger.debug(f"Would store alert to database: {alert['title']}")
    
    async def _send_notification(self, alert: Dict) -> None:
        """Send notification via webhook or other channels"""
        if not settings.notifications.webhook_url:
            logger.debug("No webhook URL configured, skipping notification")
            return
        
        try:
            # Prepare notification payload
            payload = {
                "text": f"🚨 Stock AI Alert: {alert['title']}",
                "attachments": [
                    {
                        "color": self._get_color_for_level(alert['level']),
                        "fields": [
                            {
                                "title": "Level",
                                "value": alert['level'],
                                "short": True
                            },
                            {
                                "title": "Type",
                                "value": alert['alert_type'],
                                "short": True
                            },
                            {
                                "title": "Message",
                                "value": alert['message'],
                                "short": False
                            }
                        ],
                        "footer": f"Stock AI Agents | {alert['timestamp']}"
                    }
                ]
            }
            
            if alert['symbol']:
                payload["attachments"][0]["fields"].insert(2, {
                    "title": "Symbol",
                    "value": alert['symbol'],
                    "short": True
                })
            
            if alert['agent_name']:
                payload["attachments"][0]["fields"].insert(-1, {
                    "title": "Agent",
                    "value": alert['agent_name'],
                    "short": True
                })
            
            # Send webhook
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    settings.notifications.webhook_url,
                    json=payload,
                    timeout=10.0
                )
                response.raise_for_status()
                
            logger.info(f"Sent notification for alert: {alert['title']}")
            
        except Exception as e:
            logger.error(f"Failed to send notification: {e}")
    
    def _get_color_for_level(self, level: str) -> str:
        """Get color code for alert level"""
        colors = {
            "INFO": "#36a64f",      # Green
            "WARNING": "#ff9500",   # Orange
            "ERROR": "#ff0000",     # Red
            "CRITICAL": "#8b0000"   # Dark Red
        }
        return colors.get(level, "#36a64f")


class NotificationTool(BaseTool):
    """Tool for sending various types of notifications"""
    
    def __init__(self):
        super().__init__(
            name="notification_tool",
            description="Send notifications about trading activities, performance updates, and system events"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="notification_type",
                    type="string",
                    description="Type of notification",
                    enum=["trade_execution", "performance_update", "plan_update", "system_status", "daily_summary"]
                ),
                ToolParameter(
                    name="title",
                    type="string",
                    description="Notification title"
                ),
                ToolParameter(
                    name="message",
                    type="string",
                    description="Notification message"
                ),
                ToolParameter(
                    name="data",
                    type="object",
                    description="Additional notification data",
                    required=False
                ),
                ToolParameter(
                    name="recipients",
                    type="array",
                    description="List of recipients (if applicable)",
                    required=False
                ),
                ToolParameter(
                    name="priority",
                    type="string",
                    description="Notification priority",
                    enum=["LOW", "NORMAL", "HIGH"],
                    default="NORMAL"
                ),
            ]
        )
    
    async def execute(self, notification_type: str, title: str, message: str,
                     data: Dict = None, recipients: List[str] = None, 
                     priority: str = "NORMAL") -> ToolResult:
        """Send a notification"""
        
        try:
            # Create notification data
            notification = {
                "timestamp": datetime.now().isoformat(),
                "notification_type": notification_type,
                "title": title,
                "message": message,
                "priority": priority,
                "recipients": recipients or [],
                "data": data or {},
            }
            
            # Log the notification
            logger.info(f"[{priority}] {notification_type}: {title}")
            
            # Send notification based on type
            if notification_type == "trade_execution":
                await self._send_trade_notification(notification)
            elif notification_type == "performance_update":
                await self._send_performance_notification(notification)
            elif notification_type == "plan_update":
                await self._send_plan_notification(notification)
            elif notification_type == "system_status":
                await self._send_system_notification(notification)
            elif notification_type == "daily_summary":
                await self._send_summary_notification(notification)
            else:
                await self._send_generic_notification(notification)
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data=notification,
                message=f"Sent {notification_type} notification: {title}"
            )
            
        except Exception as e:
            raise ToolError(f"Failed to send notification: {str(e)}")
    
    async def _send_trade_notification(self, notification: Dict) -> None:
        """Send trade execution notification"""
        if not settings.notifications.enabled or not settings.notifications.webhook_url:
            return
        
        data = notification.get("data", {})
        
        payload = {
            "text": f"📈 Trade Executed: {notification['title']}",
            "attachments": [
                {
                    "color": "#36a64f" if data.get("action") == "BUY" else "#ff9500",
                    "fields": [
                        {
                            "title": "Action",
                            "value": data.get("action", "N/A"),
                            "short": True
                        },
                        {
                            "title": "Symbol",
                            "value": data.get("symbol", "N/A"),
                            "short": True
                        },
                        {
                            "title": "Quantity",
                            "value": str(data.get("quantity", "N/A")),
                            "short": True
                        },
                        {
                            "title": "Price",
                            "value": f"${data.get('price', 'N/A')}",
                            "short": True
                        },
                        {
                            "title": "Total Amount",
                            "value": f"${data.get('amount', 'N/A')}",
                            "short": True
                        },
                        {
                            "title": "Account",
                            "value": data.get("account", "default"),
                            "short": True
                        }
                    ],
                    "footer": f"Stock AI Agents | {notification['timestamp']}"
                }
            ]
        }
        
        await self._send_webhook(payload)
    
    async def _send_performance_notification(self, notification: Dict) -> None:
        """Send performance update notification"""
        if not settings.notifications.enabled or not settings.notifications.webhook_url:
            return
        
        data = notification.get("data", {})
        
        # Determine color based on performance
        return_pct = data.get("return_percentage", 0)
        color = "#36a64f" if return_pct > 0 else "#ff0000" if return_pct < -5 else "#ff9500"
        
        payload = {
            "text": f"📊 Performance Update: {notification['title']}",
            "attachments": [
                {
                    "color": color,
                    "fields": [
                        {
                            "title": "Portfolio Value",
                            "value": f"${data.get('portfolio_value', 'N/A'):,.2f}",
                            "short": True
                        },
                        {
                            "title": "Total Return",
                            "value": f"{data.get('return_percentage', 'N/A')}%",
                            "short": True
                        },
                        {
                            "title": "Cash Position",
                            "value": f"${data.get('cash_position', 'N/A'):,.2f}",
                            "short": True
                        },
                        {
                            "title": "Positions",
                            "value": str(data.get("active_positions", "N/A")),
                            "short": True
                        }
                    ],
                    "footer": f"Stock AI Agents | {notification['timestamp']}"
                }
            ]
        }
        
        await self._send_webhook(payload)
    
    async def _send_plan_notification(self, notification: Dict) -> None:
        """Send plan update notification"""
        if not settings.notifications.enabled or not settings.notifications.webhook_url:
            return
        
        data = notification.get("data", {})
        
        payload = {
            "text": f"📋 Plan Update: {notification['title']}",
            "attachments": [
                {
                    "color": "#36a64f",
                    "fields": [
                        {
                            "title": "Plan Name",
                            "value": data.get("plan_name", "N/A"),
                            "short": True
                        },
                        {
                            "title": "Update Type",
                            "value": data.get("update_type", "N/A"),
                            "short": True
                        },
                        {
                            "title": "Summary",
                            "value": data.get("summary", notification["message"]),
                            "short": False
                        }
                    ],
                    "footer": f"Stock AI Agents | {notification['timestamp']}"
                }
            ]
        }
        
        await self._send_webhook(payload)
    
    async def _send_system_notification(self, notification: Dict) -> None:
        """Send system status notification"""
        if not settings.notifications.enabled or not settings.notifications.webhook_url:
            return
        
        data = notification.get("data", {})
        status = data.get("status", "UNKNOWN")
        
        # Color based on status
        color_map = {
            "ONLINE": "#36a64f",
            "OFFLINE": "#ff0000",
            "MAINTENANCE": "#ff9500",
            "ERROR": "#ff0000",
            "WARNING": "#ff9500"
        }
        color = color_map.get(status, "#36a64f")
        
        payload = {
            "text": f"🔧 System Status: {notification['title']}",
            "attachments": [
                {
                    "color": color,
                    "fields": [
                        {
                            "title": "Status",
                            "value": status,
                            "short": True
                        },
                        {
                            "title": "Message",
                            "value": notification["message"],
                            "short": False
                        }
                    ],
                    "footer": f"Stock AI Agents | {notification['timestamp']}"
                }
            ]
        }
        
        if data.get("uptime"):
            payload["attachments"][0]["fields"].insert(1, {
                "title": "Uptime",
                "value": data["uptime"],
                "short": True
            })
        
        await self._send_webhook(payload)
    
    async def _send_summary_notification(self, notification: Dict) -> None:
        """Send daily summary notification"""
        if not settings.notifications.enabled or not settings.notifications.webhook_url:
            return
        
        data = notification.get("data", {})
        
        payload = {
            "text": f"📈 Daily Summary: {notification['title']}",
            "attachments": [
                {
                    "color": "#36a64f",
                    "fields": [
                        {
                            "title": "Trades Today",
                            "value": str(data.get("trades_count", 0)),
                            "short": True
                        },
                        {
                            "title": "Portfolio Change",
                            "value": f"{data.get('daily_return', 0):+.2f}%",
                            "short": True
                        },
                        {
                            "title": "Best Performer",
                            "value": data.get("best_performer", "N/A"),
                            "short": True
                        },
                        {
                            "title": "Worst Performer",
                            "value": data.get("worst_performer", "N/A"),
                            "short": True
                        }
                    ],
                    "footer": f"Stock AI Agents | {notification['timestamp']}"
                }
            ]
        }
        
        await self._send_webhook(payload)
    
    async def _send_generic_notification(self, notification: Dict) -> None:
        """Send generic notification"""
        if not settings.notifications.enabled or not settings.notifications.webhook_url:
            return
        
        payload = {
            "text": f"📢 {notification['title']}",
            "attachments": [
                {
                    "color": "#36a64f",
                    "fields": [
                        {
                            "title": "Type",
                            "value": notification["notification_type"],
                            "short": True
                        },
                        {
                            "title": "Priority",
                            "value": notification["priority"],
                            "short": True
                        },
                        {
                            "title": "Message",
                            "value": notification["message"],
                            "short": False
                        }
                    ],
                    "footer": f"Stock AI Agents | {notification['timestamp']}"
                }
            ]
        }
        
        await self._send_webhook(payload)
    
    async def _send_webhook(self, payload: Dict) -> None:
        """Send webhook notification"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    settings.notifications.webhook_url,
                    json=payload,
                    timeout=10.0
                )
                response.raise_for_status()
                
        except Exception as e:
            logger.error(f"Failed to send webhook notification: {e}")
