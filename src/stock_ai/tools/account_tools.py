"""
Account management tools for Stock AI Agents system
"""

from decimal import Decimal
from typing import Dict, List, Optional, Any
from datetime import datetime

from sqlalchemy import select, and_
from sqlalchemy.orm import selectinload

from .base import BaseTool, ToolResult, ToolStatus, ToolDefinition, ToolParameter, ToolError
from ..database import get_db_session
from ..models import Account, Transaction, Position
from ..models.account import TransactionType, TransactionStatus


class AccountTool(BaseTool):
    """Tool for managing stock trading accounts"""
    
    def __init__(self):
        super().__init__(
            name="account_tool",
            description="Manage stock trading accounts - view balance, cash, positions, and account details"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="action",
                    type="string",
                    description="Action to perform",
                    enum=["get_balance", "get_account_info", "update_account", "get_performance"]
                ),
                ToolParameter(
                    name="account_name",
                    type="string", 
                    description="Account name",
                    default="default"
                ),
                ToolParameter(
                    name="update_data",
                    type="object",
                    description="Data to update (for update_account action)",
                    required=False
                ),
            ]
        )
    
    async def execute(self, action: str, account_name: str = "default", update_data: Dict = None) -> ToolResult:
        """Execute account management action"""
        
        async for session in get_db_session():
            try:
                # Get account
                result = await session.execute(
                    select(Account)
                    .options(selectinload(Account.positions), selectinload(Account.transactions))
                    .where(Account.name == account_name)
                )
                account = result.scalar_one_or_none()
                
                if not account:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Account '{account_name}' not found"
                    )
                
                if action == "get_balance":
                    return await self._get_balance(account)
                elif action == "get_account_info":
                    return await self._get_account_info(account)
                elif action == "update_account":
                    return await self._update_account(session, account, update_data or {})
                elif action == "get_performance":
                    return await self._get_performance(account)
                else:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Unknown action: {action}"
                    )
                    
            except Exception as e:
                await session.rollback()
                raise ToolError(f"Account tool execution failed: {str(e)}")
    
    async def _get_balance(self, account: Account) -> ToolResult:
        """Get account balance information"""
        # Calculate total position value
        total_position_value = Decimal("0")
        for position in account.positions:
            if position.is_active and position.market_value:
                total_position_value += position.market_value
        
        # Update total value
        total_value = account.current_cash + total_position_value
        
        balance_info = {
            "account_name": account.name,
            "current_cash": float(account.current_cash),
            "total_position_value": float(total_position_value),
            "total_value": float(total_value),
            "initial_capital": float(account.initial_capital),
            "total_return": float(total_value - account.initial_capital),
            "total_return_percentage": float((total_value - account.initial_capital) / account.initial_capital * 100),
            "number_of_positions": len([p for p in account.positions if p.is_active]),
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=balance_info,
            message=f"Retrieved balance for account '{account.name}'"
        )
    
    async def _get_account_info(self, account: Account) -> ToolResult:
        """Get detailed account information"""
        account_info = {
            "id": account.id,
            "name": account.name,
            "account_type": account.account_type,
            "initial_capital": float(account.initial_capital),
            "current_cash": float(account.current_cash),
            "total_value": float(account.total_value),
            "is_active": account.is_active,
            "max_position_size": float(account.max_position_size),
            "stop_loss_percentage": float(account.stop_loss_percentage),
            "take_profit_percentage": float(account.take_profit_percentage),
            "created_at": account.created_at.isoformat(),
            "updated_at": account.updated_at.isoformat(),
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=account_info,
            message=f"Retrieved account info for '{account.name}'"
        )
    
    async def _update_account(self, session, account: Account, update_data: Dict) -> ToolResult:
        """Update account settings"""
        allowed_fields = [
            "max_position_size", "stop_loss_percentage", "take_profit_percentage", "is_active"
        ]
        
        updated_fields = []
        for field, value in update_data.items():
            if field in allowed_fields:
                if hasattr(account, field):
                    setattr(account, field, value)
                    updated_fields.append(field)
        
        if updated_fields:
            await session.commit()
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data={"updated_fields": updated_fields},
                message=f"Updated account '{account.name}': {', '.join(updated_fields)}"
            )
        else:
            return ToolResult(
                status=ToolStatus.WARNING,
                message="No valid fields to update"
            )
    
    async def _get_performance(self, account: Account) -> ToolResult:
        """Get account performance metrics"""
        # Calculate performance metrics
        total_position_value = sum(
            float(p.market_value or 0) for p in account.positions if p.is_active
        )
        total_value = float(account.current_cash) + total_position_value
        total_return = total_value - float(account.initial_capital)
        total_return_pct = (total_return / float(account.initial_capital)) * 100
        
        # Get recent transactions for activity analysis
        recent_transactions = sorted(
            [t for t in account.transactions if t.executed_at],
            key=lambda x: x.executed_at,
            reverse=True
        )[:10]
        
        performance = {
            "total_value": total_value,
            "total_return": total_return,
            "total_return_percentage": total_return_pct,
            "cash_percentage": (float(account.current_cash) / total_value) * 100 if total_value > 0 else 0,
            "invested_percentage": (total_position_value / total_value) * 100 if total_value > 0 else 0,
            "recent_activity": [
                {
                    "type": t.transaction_type.value,
                    "symbol": t.stock_symbol,
                    "quantity": float(t.quantity) if t.quantity else None,
                    "price": float(t.price) if t.price else None,
                    "amount": float(t.amount),
                    "executed_at": t.executed_at.isoformat() if t.executed_at else None,
                }
                for t in recent_transactions
            ]
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=performance,
            message=f"Retrieved performance metrics for account '{account.name}'"
        )


class PositionTool(BaseTool):
    """Tool for managing stock positions"""
    
    def __init__(self):
        super().__init__(
            name="position_tool",
            description="Manage stock positions - view, update, and analyze current holdings"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="action",
                    type="string",
                    description="Action to perform",
                    enum=["get_positions", "get_position", "update_position", "close_position"]
                ),
                ToolParameter(
                    name="account_name",
                    type="string",
                    description="Account name",
                    default="default"
                ),
                ToolParameter(
                    name="symbol",
                    type="string",
                    description="Stock symbol (required for single position actions)",
                    required=False
                ),
                ToolParameter(
                    name="update_data",
                    type="object",
                    description="Data to update (for update_position action)",
                    required=False
                ),
            ]
        )
    
    async def execute(self, action: str, account_name: str = "default", 
                     symbol: str = None, update_data: Dict = None) -> ToolResult:
        """Execute position management action"""
        
        async for session in get_db_session():
            try:
                # Get account
                result = await session.execute(
                    select(Account).where(Account.name == account_name)
                )
                account = result.scalar_one_or_none()
                
                if not account:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Account '{account_name}' not found"
                    )
                
                if action == "get_positions":
                    return await self._get_positions(session, account)
                elif action == "get_position":
                    if not symbol:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Symbol is required for get_position action"
                        )
                    return await self._get_position(session, account, symbol)
                elif action == "update_position":
                    if not symbol:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Symbol is required for update_position action"
                        )
                    return await self._update_position(session, account, symbol, update_data or {})
                elif action == "close_position":
                    if not symbol:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Symbol is required for close_position action"
                        )
                    return await self._close_position(session, account, symbol)
                else:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Unknown action: {action}"
                    )
                    
            except Exception as e:
                await session.rollback()
                raise ToolError(f"Position tool execution failed: {str(e)}")
    
    async def _get_positions(self, session, account: Account) -> ToolResult:
        """Get all active positions"""
        result = await session.execute(
            select(Position).where(
                and_(Position.account_id == account.id, Position.is_active == True)
            )
        )
        positions = result.scalars().all()
        
        positions_data = []
        for position in positions:
            position_data = {
                "symbol": position.stock_symbol,
                "quantity": float(position.quantity),
                "average_cost": float(position.average_cost),
                "current_price": float(position.current_price) if position.current_price else None,
                "market_value": float(position.market_value) if position.market_value else None,
                "unrealized_pnl": float(position.unrealized_pnl) if position.unrealized_pnl else None,
                "unrealized_pnl_percentage": float(position.unrealized_pnl_percentage) if position.unrealized_pnl_percentage else None,
                "stop_loss_price": float(position.stop_loss_price) if position.stop_loss_price else None,
                "take_profit_price": float(position.take_profit_price) if position.take_profit_price else None,
                "last_updated": position.last_updated.isoformat() if position.last_updated else None,
            }
            positions_data.append(position_data)
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=positions_data,
            message=f"Retrieved {len(positions_data)} active positions for account '{account.name}'"
        )
    
    async def _get_position(self, session, account: Account, symbol: str) -> ToolResult:
        """Get specific position"""
        result = await session.execute(
            select(Position).where(
                and_(
                    Position.account_id == account.id,
                    Position.stock_symbol == symbol,
                    Position.is_active == True
                )
            )
        )
        position = result.scalar_one_or_none()
        
        if not position:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"No active position found for symbol '{symbol}'"
            )
        
        position_data = {
            "symbol": position.stock_symbol,
            "quantity": float(position.quantity),
            "average_cost": float(position.average_cost),
            "current_price": float(position.current_price) if position.current_price else None,
            "market_value": float(position.market_value) if position.market_value else None,
            "unrealized_pnl": float(position.unrealized_pnl) if position.unrealized_pnl else None,
            "unrealized_pnl_percentage": float(position.unrealized_pnl_percentage) if position.unrealized_pnl_percentage else None,
            "stop_loss_price": float(position.stop_loss_price) if position.stop_loss_price else None,
            "take_profit_price": float(position.take_profit_price) if position.take_profit_price else None,
            "last_updated": position.last_updated.isoformat() if position.last_updated else None,
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=position_data,
            message=f"Retrieved position for '{symbol}'"
        )
    
    async def _update_position(self, session, account: Account, symbol: str, update_data: Dict) -> ToolResult:
        """Update position settings"""
        result = await session.execute(
            select(Position).where(
                and_(
                    Position.account_id == account.id,
                    Position.stock_symbol == symbol,
                    Position.is_active == True
                )
            )
        )
        position = result.scalar_one_or_none()
        
        if not position:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"No active position found for symbol '{symbol}'"
            )
        
        allowed_fields = ["stop_loss_price", "take_profit_price", "current_price"]
        updated_fields = []
        
        for field, value in update_data.items():
            if field in allowed_fields and hasattr(position, field):
                setattr(position, field, Decimal(str(value)) if value is not None else None)
                updated_fields.append(field)
        
        if "current_price" in update_data and update_data["current_price"]:
            # Recalculate market value and P&L
            current_price = Decimal(str(update_data["current_price"]))
            position.current_price = current_price
            position.market_value = position.quantity * current_price
            position.unrealized_pnl = position.market_value - (position.quantity * position.average_cost)
            position.unrealized_pnl_percentage = (position.unrealized_pnl / (position.quantity * position.average_cost)) * 100
            position.last_updated = datetime.now()
            updated_fields.extend(["market_value", "unrealized_pnl", "unrealized_pnl_percentage"])
        
        if updated_fields:
            await session.commit()
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data={"updated_fields": updated_fields},
                message=f"Updated position for '{symbol}': {', '.join(updated_fields)}"
            )
        else:
            return ToolResult(
                status=ToolStatus.WARNING,
                message="No valid fields to update"
            )
    
    async def _close_position(self, session, account: Account, symbol: str) -> ToolResult:
        """Close (deactivate) a position"""
        result = await session.execute(
            select(Position).where(
                and_(
                    Position.account_id == account.id,
                    Position.stock_symbol == symbol,
                    Position.is_active == True
                )
            )
        )
        position = result.scalar_one_or_none()
        
        if not position:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"No active position found for symbol '{symbol}'"
            )
        
        position.is_active = False
        await session.commit()
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            message=f"Closed position for '{symbol}'"
        )


class TransactionTool(BaseTool):
    """Tool for managing transactions"""
    
    def __init__(self):
        super().__init__(
            name="transaction_tool",
            description="Record and manage stock transactions - buy, sell, and track trading history"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="action",
                    type="string",
                    description="Action to perform",
                    enum=["record_transaction", "get_transactions", "update_transaction", "execute_transaction"]
                ),
                ToolParameter(
                    name="account_name",
                    type="string",
                    description="Account name",
                    default="default"
                ),
                ToolParameter(
                    name="transaction_data",
                    type="object",
                    description="Transaction data (required for record_transaction)",
                    required=False
                ),
                ToolParameter(
                    name="transaction_id",
                    type="number",
                    description="Transaction ID (for update/execute actions)",
                    required=False
                ),
                ToolParameter(
                    name="limit",
                    type="number",
                    description="Maximum number of transactions to retrieve",
                    default=50,
                    minimum=1,
                    maximum=1000
                ),
            ]
        )
    
    async def execute(self, action: str, account_name: str = "default",
                     transaction_data: Dict = None, transaction_id: int = None,
                     limit: int = 50) -> ToolResult:
        """Execute transaction management action"""
        
        async for session in get_db_session():
            try:
                # Get account
                result = await session.execute(
                    select(Account).where(Account.name == account_name)
                )
                account = result.scalar_one_or_none()
                
                if not account:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Account '{account_name}' not found"
                    )
                
                if action == "record_transaction":
                    if not transaction_data:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Transaction data is required for record_transaction action"
                        )
                    return await self._record_transaction(session, account, transaction_data)
                elif action == "get_transactions":
                    return await self._get_transactions(session, account, limit)
                elif action == "update_transaction":
                    if not transaction_id:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Transaction ID is required for update_transaction action"
                        )
                    return await self._update_transaction(session, transaction_id, transaction_data or {})
                elif action == "execute_transaction":
                    if not transaction_id:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Transaction ID is required for execute_transaction action"
                        )
                    return await self._execute_transaction(session, transaction_id)
                else:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Unknown action: {action}"
                    )
                    
            except Exception as e:
                await session.rollback()
                raise ToolError(f"Transaction tool execution failed: {str(e)}")
    
    async def _record_transaction(self, session, account: Account, transaction_data: Dict) -> ToolResult:
        """Record a new transaction"""
        required_fields = ["transaction_type", "amount"]
        for field in required_fields:
            if field not in transaction_data:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Required field '{field}' is missing from transaction data"
                )
        
        # Create transaction
        transaction = Transaction(
            account_id=account.id,
            stock_symbol=transaction_data.get("stock_symbol"),
            transaction_type=TransactionType(transaction_data["transaction_type"]),
            status=TransactionStatus.PENDING,
            quantity=Decimal(str(transaction_data["quantity"])) if transaction_data.get("quantity") else None,
            price=Decimal(str(transaction_data["price"])) if transaction_data.get("price") else None,
            amount=Decimal(str(transaction_data["amount"])),
            fees=Decimal(str(transaction_data.get("fees", 0))),
            notes=transaction_data.get("notes"),
        )
        
        session.add(transaction)
        await session.commit()
        await session.refresh(transaction)
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data={"transaction_id": transaction.id},
            message=f"Recorded {transaction.transaction_type.value} transaction for {transaction.stock_symbol or 'cash'}"
        )
    
    async def _get_transactions(self, session, account: Account, limit: int) -> ToolResult:
        """Get transaction history"""
        result = await session.execute(
            select(Transaction)
            .where(Transaction.account_id == account.id)
            .order_by(Transaction.created_at.desc())
            .limit(limit)
        )
        transactions = result.scalars().all()
        
        transactions_data = []
        for transaction in transactions:
            transaction_data = {
                "id": transaction.id,
                "stock_symbol": transaction.stock_symbol,
                "transaction_type": transaction.transaction_type.value,
                "status": transaction.status.value,
                "quantity": float(transaction.quantity) if transaction.quantity else None,
                "price": float(transaction.price) if transaction.price else None,
                "amount": float(transaction.amount),
                "fees": float(transaction.fees),
                "created_at": transaction.created_at.isoformat(),
                "executed_at": transaction.executed_at.isoformat() if transaction.executed_at else None,
                "notes": transaction.notes,
            }
            transactions_data.append(transaction_data)
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=transactions_data,
            message=f"Retrieved {len(transactions_data)} transactions for account '{account.name}'"
        )
    
    async def _update_transaction(self, session, transaction_id: int, update_data: Dict) -> ToolResult:
        """Update transaction"""
        result = await session.execute(
            select(Transaction).where(Transaction.id == transaction_id)
        )
        transaction = result.scalar_one_or_none()
        
        if not transaction:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Transaction with ID {transaction_id} not found"
            )
        
        allowed_fields = ["status", "price", "fees", "notes", "executed_at"]
        updated_fields = []
        
        for field, value in update_data.items():
            if field in allowed_fields and hasattr(transaction, field):
                if field == "status":
                    setattr(transaction, field, TransactionStatus(value))
                elif field == "price" or field == "fees":
                    setattr(transaction, field, Decimal(str(value)) if value is not None else None)
                elif field == "executed_at":
                    setattr(transaction, field, datetime.fromisoformat(value) if value else None)
                else:
                    setattr(transaction, field, value)
                updated_fields.append(field)
        
        if updated_fields:
            await session.commit()
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data={"updated_fields": updated_fields},
                message=f"Updated transaction {transaction_id}: {', '.join(updated_fields)}"
            )
        else:
            return ToolResult(
                status=ToolStatus.WARNING,
                message="No valid fields to update"
            )
    
    async def _execute_transaction(self, session, transaction_id: int) -> ToolResult:
        """Execute a pending transaction"""
        result = await session.execute(
            select(Transaction).where(Transaction.id == transaction_id)
        )
        transaction = result.scalar_one_or_none()
        
        if not transaction:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Transaction with ID {transaction_id} not found"
            )
        
        if transaction.status != TransactionStatus.PENDING:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Transaction {transaction_id} is not in PENDING status"
            )
        
        # Mark as executed
        transaction.status = TransactionStatus.EXECUTED
        transaction.executed_at = datetime.now()
        
        # Update account and positions (simplified - would need more complex logic in real implementation)
        if transaction.transaction_type in [TransactionType.BUY, TransactionType.SELL]:
            # This would typically involve updating positions and account cash
            # For now, just mark as executed
            pass
        
        await session.commit()
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            message=f"Executed transaction {transaction_id}"
        )
