"""
Base tool classes and interfaces for Stock AI Agents system
"""

import json
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel, Field
from loguru import logger


class ToolStatus(str, Enum):
    """Tool execution status"""
    SUCCESS = "SUCCESS"
    ERROR = "ERROR"
    WARNING = "WARNING"
    PENDING = "PENDING"


@dataclass
class ToolResult:
    """Tool execution result"""
    status: ToolStatus
    data: Any = None
    message: str = ""
    error: Optional[str] = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        self.metadata["timestamp"] = datetime.now().isoformat()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            "status": self.status.value,
            "data": self.data,
            "message": self.message,
            "error": self.error,
            "metadata": self.metadata,
        }
    
    def to_json(self) -> str:
        """Convert to JSON string"""
        return json.dumps(self.to_dict(), default=str)


class ToolError(Exception):
    """Tool execution error"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}


class ToolParameter(BaseModel):
    """Tool parameter definition"""
    name: str
    type: str  # "string", "number", "boolean", "array", "object"
    description: str
    required: bool = True
    default: Any = None
    enum: Optional[List[str]] = None
    minimum: Optional[float] = None
    maximum: Optional[float] = None


class ToolDefinition(BaseModel):
    """Tool definition for MCP protocol"""
    name: str
    description: str
    parameters: List[ToolParameter]
    
    def to_mcp_schema(self) -> Dict[str, Any]:
        """Convert to MCP tool schema"""
        properties = {}
        required = []
        
        for param in self.parameters:
            prop = {
                "type": param.type,
                "description": param.description,
            }
            
            if param.enum:
                prop["enum"] = param.enum
            if param.minimum is not None:
                prop["minimum"] = param.minimum
            if param.maximum is not None:
                prop["maximum"] = param.maximum
            if param.default is not None:
                prop["default"] = param.default
            
            properties[param.name] = prop
            
            if param.required:
                required.append(param.name)
        
        return {
            "name": self.name,
            "description": self.description,
            "inputSchema": {
                "type": "object",
                "properties": properties,
                "required": required,
            }
        }


class BaseTool(ABC):
    """Base class for all tools"""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
        self._definition: Optional[ToolDefinition] = None
    
    @property
    def definition(self) -> ToolDefinition:
        """Get tool definition"""
        if self._definition is None:
            self._definition = self._create_definition()
        return self._definition
    
    @abstractmethod
    def _create_definition(self) -> ToolDefinition:
        """Create tool definition"""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the tool"""
        pass
    
    def validate_parameters(self, **kwargs) -> Dict[str, Any]:
        """Validate and process parameters"""
        validated = {}
        
        for param in self.definition.parameters:
            value = kwargs.get(param.name)
            
            # Check required parameters
            if param.required and value is None:
                if param.default is not None:
                    value = param.default
                else:
                    raise ToolError(f"Required parameter '{param.name}' is missing")
            
            # Type validation (basic)
            if value is not None:
                if param.type == "number" and not isinstance(value, (int, float)):
                    try:
                        value = float(value)
                    except (ValueError, TypeError):
                        raise ToolError(f"Parameter '{param.name}' must be a number")
                
                elif param.type == "boolean" and not isinstance(value, bool):
                    if isinstance(value, str):
                        value = value.lower() in ("true", "1", "yes", "on")
                    else:
                        value = bool(value)
                
                elif param.type == "string" and not isinstance(value, str):
                    value = str(value)
                
                # Enum validation
                if param.enum and value not in param.enum:
                    raise ToolError(f"Parameter '{param.name}' must be one of: {param.enum}")
                
                # Range validation
                if param.type == "number":
                    if param.minimum is not None and value < param.minimum:
                        raise ToolError(f"Parameter '{param.name}' must be >= {param.minimum}")
                    if param.maximum is not None and value > param.maximum:
                        raise ToolError(f"Parameter '{param.name}' must be <= {param.maximum}")
            
            validated[param.name] = value
        
        return validated
    
    async def safe_execute(self, **kwargs) -> ToolResult:
        """Execute tool with error handling"""
        try:
            # Validate parameters
            validated_params = self.validate_parameters(**kwargs)
            
            # Log execution
            logger.info(f"Executing tool: {self.name} with params: {validated_params}")
            
            # Execute tool
            result = await self.execute(**validated_params)
            
            # Log result
            logger.info(f"Tool {self.name} completed with status: {result.status}")
            
            return result
            
        except ToolError as e:
            logger.error(f"Tool {self.name} failed: {e.message}")
            return ToolResult(
                status=ToolStatus.ERROR,
                error=e.message,
                metadata={"error_code": e.error_code, "details": e.details}
            )
        
        except Exception as e:
            logger.error(f"Tool {self.name} failed with unexpected error: {str(e)}")
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"Unexpected error: {str(e)}",
                metadata={"error_type": type(e).__name__}
            )
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}')>"
