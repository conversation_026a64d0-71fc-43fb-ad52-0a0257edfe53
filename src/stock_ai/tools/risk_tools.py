"""
Risk analysis and portfolio management tools for Stock AI Agents system
"""

from decimal import Decimal
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import math

from sqlalchemy import select, and_, desc

from .base import BaseTool, ToolResult, ToolStatus, ToolDefinition, ToolParameter, ToolError
from ..database import get_db_session
from ..models import Account, Position, Transaction, MarketData, Stock


class RiskAnalysisTool(BaseTool):
    """Tool for analyzing investment risks"""
    
    def __init__(self):
        super().__init__(
            name="risk_analysis_tool",
            description="Analyze portfolio and position-level risks, calculate risk metrics"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="analysis_type",
                    type="string",
                    description="Type of risk analysis to perform",
                    enum=["portfolio_risk", "position_risk", "var_analysis", "correlation_analysis"]
                ),
                ToolParameter(
                    name="account_name",
                    type="string",
                    description="Account name",
                    default="default"
                ),
                ToolParameter(
                    name="symbol",
                    type="string",
                    description="Stock symbol (for position-specific analysis)",
                    required=False
                ),
                ToolParameter(
                    name="confidence_level",
                    type="number",
                    description="Confidence level for VaR calculation (0.90, 0.95, 0.99)",
                    default=0.95,
                    minimum=0.80,
                    maximum=0.99
                ),
                ToolParameter(
                    name="time_horizon",
                    type="number",
                    description="Time horizon in days",
                    default=1,
                    minimum=1,
                    maximum=30
                ),
            ]
        )
    
    async def execute(self, analysis_type: str, account_name: str = "default",
                     symbol: str = None, confidence_level: float = 0.95,
                     time_horizon: int = 1) -> ToolResult:
        """Execute risk analysis"""
        
        async for session in get_db_session():
            try:
                # Get account
                result = await session.execute(
                    select(Account).where(Account.name == account_name)
                )
                account = result.scalar_one_or_none()
                
                if not account:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Account '{account_name}' not found"
                    )
                
                if analysis_type == "portfolio_risk":
                    return await self._analyze_portfolio_risk(session, account)
                elif analysis_type == "position_risk":
                    if not symbol:
                        return ToolResult(
                            status=ToolStatus.ERROR,
                            error="Symbol is required for position risk analysis"
                        )
                    return await self._analyze_position_risk(session, account, symbol)
                elif analysis_type == "var_analysis":
                    return await self._calculate_var(session, account, confidence_level, time_horizon)
                elif analysis_type == "correlation_analysis":
                    return await self._analyze_correlations(session, account)
                else:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Unknown analysis type: {analysis_type}"
                    )
                    
            except Exception as e:
                raise ToolError(f"Risk analysis failed: {str(e)}")
    
    async def _analyze_portfolio_risk(self, session, account: Account) -> ToolResult:
        """Analyze overall portfolio risk"""
        # Get active positions
        result = await session.execute(
            select(Position).where(
                and_(Position.account_id == account.id, Position.is_active == True)
            )
        )
        positions = result.scalars().all()
        
        if not positions:
            return ToolResult(
                status=ToolStatus.WARNING,
                message="No active positions found for risk analysis",
                data={"total_positions": 0}
            )
        
        # Calculate portfolio metrics
        total_value = float(account.current_cash)
        position_values = []
        position_weights = []
        unrealized_pnl_values = []
        
        for position in positions:
            if position.market_value:
                position_value = float(position.market_value)
                total_value += position_value
                position_values.append(position_value)
                
                if position.unrealized_pnl:
                    unrealized_pnl_values.append(float(position.unrealized_pnl))
        
        # Calculate position weights
        for value in position_values:
            weight = value / total_value if total_value > 0 else 0
            position_weights.append(weight)
        
        # Risk metrics
        max_position_weight = max(position_weights) if position_weights else 0
        total_unrealized_pnl = sum(unrealized_pnl_values)
        total_unrealized_pnl_pct = (total_unrealized_pnl / float(account.initial_capital)) * 100
        
        # Concentration risk
        concentration_risk = "HIGH" if max_position_weight > 0.2 else "MEDIUM" if max_position_weight > 0.1 else "LOW"
        
        # Diversification score (simplified)
        num_positions = len(positions)
        diversification_score = min(100, (num_positions / 10) * 100)  # Assume 10+ positions is well diversified
        
        risk_analysis = {
            "account_name": account.name,
            "total_portfolio_value": total_value,
            "cash_percentage": (float(account.current_cash) / total_value) * 100 if total_value > 0 else 0,
            "invested_percentage": ((total_value - float(account.current_cash)) / total_value) * 100 if total_value > 0 else 0,
            "number_of_positions": num_positions,
            "max_position_weight": max_position_weight * 100,  # Convert to percentage
            "concentration_risk": concentration_risk,
            "diversification_score": diversification_score,
            "total_unrealized_pnl": total_unrealized_pnl,
            "total_unrealized_pnl_percentage": total_unrealized_pnl_pct,
            "position_breakdown": [
                {
                    "symbol": pos.stock_symbol,
                    "weight": weight * 100,
                    "value": float(pos.market_value) if pos.market_value else 0,
                    "unrealized_pnl": float(pos.unrealized_pnl) if pos.unrealized_pnl else 0,
                    "unrealized_pnl_pct": float(pos.unrealized_pnl_percentage) if pos.unrealized_pnl_percentage else 0,
                }
                for pos, weight in zip(positions, position_weights)
            ]
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=risk_analysis,
            message=f"Completed portfolio risk analysis for account '{account.name}'"
        )
    
    async def _analyze_position_risk(self, session, account: Account, symbol: str) -> ToolResult:
        """Analyze risk for a specific position"""
        # Get position
        result = await session.execute(
            select(Position).where(
                and_(
                    Position.account_id == account.id,
                    Position.stock_symbol == symbol.upper(),
                    Position.is_active == True
                )
            )
        )
        position = result.scalar_one_or_none()
        
        if not position:
            return ToolResult(
                status=ToolStatus.ERROR,
                error=f"No active position found for symbol '{symbol}'"
            )
        
        # Calculate position metrics
        position_value = float(position.market_value) if position.market_value else 0
        total_portfolio_value = float(account.current_cash) + sum(
            float(p.market_value) for p in account.positions if p.is_active and p.market_value
        )
        
        position_weight = (position_value / total_portfolio_value) * 100 if total_portfolio_value > 0 else 0
        
        # Risk assessment
        risk_level = "HIGH" if position_weight > 20 else "MEDIUM" if position_weight > 10 else "LOW"
        
        # Stop loss and take profit analysis
        current_price = float(position.current_price) if position.current_price else 0
        average_cost = float(position.average_cost)
        
        stop_loss_distance = 0
        take_profit_distance = 0
        
        if position.stop_loss_price and current_price > 0:
            stop_loss_distance = ((current_price - float(position.stop_loss_price)) / current_price) * 100
        
        if position.take_profit_price and current_price > 0:
            take_profit_distance = ((float(position.take_profit_price) - current_price) / current_price) * 100
        
        position_risk = {
            "symbol": symbol,
            "position_value": position_value,
            "position_weight": position_weight,
            "risk_level": risk_level,
            "quantity": float(position.quantity),
            "average_cost": average_cost,
            "current_price": current_price,
            "unrealized_pnl": float(position.unrealized_pnl) if position.unrealized_pnl else 0,
            "unrealized_pnl_percentage": float(position.unrealized_pnl_percentage) if position.unrealized_pnl_percentage else 0,
            "stop_loss_price": float(position.stop_loss_price) if position.stop_loss_price else None,
            "take_profit_price": float(position.take_profit_price) if position.take_profit_price else None,
            "stop_loss_distance_pct": stop_loss_distance,
            "take_profit_distance_pct": take_profit_distance,
            "risk_reward_ratio": abs(take_profit_distance / stop_loss_distance) if stop_loss_distance != 0 else None,
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=position_risk,
            message=f"Completed position risk analysis for '{symbol}'"
        )
    
    async def _calculate_var(self, session, account: Account, confidence_level: float, time_horizon: int) -> ToolResult:
        """Calculate Value at Risk (VaR) - simplified implementation"""
        # Get active positions
        result = await session.execute(
            select(Position).where(
                and_(Position.account_id == account.id, Position.is_active == True)
            )
        )
        positions = result.scalars().all()
        
        if not positions:
            return ToolResult(
                status=ToolStatus.WARNING,
                message="No active positions found for VaR calculation",
                data={"var_amount": 0, "var_percentage": 0}
            )
        
        # Simplified VaR calculation using historical volatility
        # In a real implementation, this would use more sophisticated methods
        
        total_portfolio_value = float(account.current_cash)
        position_vars = []
        
        for position in positions:
            if not position.market_value:
                continue
                
            position_value = float(position.market_value)
            total_portfolio_value += position_value
            
            # Get historical data for volatility calculation
            stock_result = await session.execute(
                select(Stock).where(Stock.symbol == position.stock_symbol)
            )
            stock = stock_result.scalar_one_or_none()
            
            if stock:
                # Get recent price data
                end_date = datetime.now()
                start_date = end_date - timedelta(days=30)  # 30 days of data
                
                data_result = await session.execute(
                    select(MarketData)
                    .where(
                        and_(
                            MarketData.stock_id == stock.id,
                            MarketData.timestamp >= start_date
                        )
                    )
                    .order_by(MarketData.timestamp)
                )
                price_data = data_result.scalars().all()
                
                if len(price_data) >= 2:
                    # Calculate daily returns
                    returns = []
                    for i in range(1, len(price_data)):
                        prev_price = float(price_data[i-1].close_price)
                        curr_price = float(price_data[i].close_price)
                        daily_return = (curr_price - prev_price) / prev_price
                        returns.append(daily_return)
                    
                    if returns:
                        # Calculate volatility (standard deviation of returns)
                        mean_return = sum(returns) / len(returns)
                        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
                        volatility = math.sqrt(variance)
                        
                        # Scale volatility for time horizon
                        scaled_volatility = volatility * math.sqrt(time_horizon)
                        
                        # Calculate VaR using normal distribution approximation
                        # Z-scores for common confidence levels: 90%=1.28, 95%=1.65, 99%=2.33
                        z_score = 1.28 if confidence_level == 0.90 else 1.65 if confidence_level == 0.95 else 2.33
                        
                        position_var = position_value * z_score * scaled_volatility
                        position_vars.append(position_var)
        
        # Portfolio VaR (simplified - assumes no correlation)
        portfolio_var = math.sqrt(sum(var ** 2 for var in position_vars)) if position_vars else 0
        var_percentage = (portfolio_var / total_portfolio_value) * 100 if total_portfolio_value > 0 else 0
        
        var_analysis = {
            "confidence_level": confidence_level,
            "time_horizon_days": time_horizon,
            "portfolio_value": total_portfolio_value,
            "var_amount": portfolio_var,
            "var_percentage": var_percentage,
            "interpretation": f"There is a {(1-confidence_level)*100}% chance of losing more than ${portfolio_var:.2f} ({var_percentage:.2f}%) over {time_horizon} day(s)",
            "risk_level": "HIGH" if var_percentage > 5 else "MEDIUM" if var_percentage > 2 else "LOW",
            "note": "This is a simplified VaR calculation. Production systems would use more sophisticated methods."
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=var_analysis,
            message=f"Calculated VaR at {confidence_level*100}% confidence level"
        )
    
    async def _analyze_correlations(self, session, account: Account) -> ToolResult:
        """Analyze correlations between positions - simplified implementation"""
        # Get active positions
        result = await session.execute(
            select(Position).where(
                and_(Position.account_id == account.id, Position.is_active == True)
            )
        )
        positions = result.scalars().all()
        
        if len(positions) < 2:
            return ToolResult(
                status=ToolStatus.WARNING,
                message="Need at least 2 positions for correlation analysis",
                data={"positions": len(positions)}
            )
        
        # Group positions by sector for sector concentration analysis
        sector_exposure = {}
        total_invested = 0
        
        for position in positions:
            if not position.market_value:
                continue
                
            position_value = float(position.market_value)
            total_invested += position_value
            
            # Get stock sector
            stock_result = await session.execute(
                select(Stock).where(Stock.symbol == position.stock_symbol)
            )
            stock = stock_result.scalar_one_or_none()
            
            if stock and stock.sector:
                sector = stock.sector
                if sector not in sector_exposure:
                    sector_exposure[sector] = 0
                sector_exposure[sector] += position_value
        
        # Calculate sector weights
        sector_weights = {}
        for sector, value in sector_exposure.items():
            sector_weights[sector] = (value / total_invested) * 100 if total_invested > 0 else 0
        
        # Identify concentration risks
        max_sector_exposure = max(sector_weights.values()) if sector_weights else 0
        concentration_risk = "HIGH" if max_sector_exposure > 40 else "MEDIUM" if max_sector_exposure > 25 else "LOW"
        
        correlation_analysis = {
            "total_positions": len(positions),
            "sectors_represented": len(sector_exposure),
            "sector_exposure": sector_weights,
            "max_sector_exposure": max_sector_exposure,
            "concentration_risk": concentration_risk,
            "diversification_score": min(100, (len(sector_exposure) / 5) * 100),  # Assume 5+ sectors is well diversified
            "recommendations": [],
            "note": "This is a simplified correlation analysis based on sector exposure. Production systems would calculate actual price correlations."
        }
        
        # Add recommendations
        if concentration_risk == "HIGH":
            correlation_analysis["recommendations"].append(f"Consider reducing exposure to {max(sector_weights, key=sector_weights.get)} sector")
        
        if len(sector_exposure) < 3:
            correlation_analysis["recommendations"].append("Consider diversifying across more sectors")
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=correlation_analysis,
            message="Completed correlation and sector concentration analysis"
        )


class PortfolioAnalysisTool(BaseTool):
    """Tool for comprehensive portfolio analysis"""
    
    def __init__(self):
        super().__init__(
            name="portfolio_analysis_tool",
            description="Perform comprehensive portfolio analysis including performance, allocation, and optimization"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="analysis_type",
                    type="string",
                    description="Type of portfolio analysis",
                    enum=["performance", "allocation", "rebalancing", "optimization"]
                ),
                ToolParameter(
                    name="account_name",
                    type="string",
                    description="Account name",
                    default="default"
                ),
                ToolParameter(
                    name="benchmark",
                    type="string",
                    description="Benchmark for comparison (e.g., SPY, QQQ)",
                    default="SPY",
                    required=False
                ),
                ToolParameter(
                    name="period",
                    type="string",
                    description="Analysis period",
                    enum=["1mo", "3mo", "6mo", "1y"],
                    default="3mo"
                ),
            ]
        )
    
    async def execute(self, analysis_type: str, account_name: str = "default",
                     benchmark: str = "SPY", period: str = "3mo") -> ToolResult:
        """Execute portfolio analysis"""
        
        async for session in get_db_session():
            try:
                # Get account
                result = await session.execute(
                    select(Account).where(Account.name == account_name)
                )
                account = result.scalar_one_or_none()
                
                if not account:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Account '{account_name}' not found"
                    )
                
                if analysis_type == "performance":
                    return await self._analyze_performance(session, account, benchmark, period)
                elif analysis_type == "allocation":
                    return await self._analyze_allocation(session, account)
                elif analysis_type == "rebalancing":
                    return await self._analyze_rebalancing_needs(session, account)
                elif analysis_type == "optimization":
                    return await self._suggest_optimization(session, account)
                else:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Unknown analysis type: {analysis_type}"
                    )
                    
            except Exception as e:
                raise ToolError(f"Portfolio analysis failed: {str(e)}")
    
    async def _analyze_performance(self, session, account: Account, benchmark: str, period: str) -> ToolResult:
        """Analyze portfolio performance"""
        # Calculate current portfolio value
        total_value = float(account.current_cash)
        for position in account.positions:
            if position.is_active and position.market_value:
                total_value += float(position.market_value)
        
        # Calculate returns
        initial_capital = float(account.initial_capital)
        total_return = total_value - initial_capital
        total_return_pct = (total_return / initial_capital) * 100
        
        # Get transaction history for period analysis
        end_date = datetime.now()
        if period == "1mo":
            start_date = end_date - timedelta(days=30)
        elif period == "3mo":
            start_date = end_date - timedelta(days=90)
        elif period == "6mo":
            start_date = end_date - timedelta(days=180)
        elif period == "1y":
            start_date = end_date - timedelta(days=365)
        else:
            start_date = end_date - timedelta(days=90)
        
        # Get transactions in period
        result = await session.execute(
            select(Transaction)
            .where(
                and_(
                    Transaction.account_id == account.id,
                    Transaction.executed_at >= start_date,
                    Transaction.executed_at <= end_date
                )
            )
            .order_by(Transaction.executed_at)
        )
        period_transactions = result.scalars().all()
        
        # Calculate period performance metrics
        period_trades = len(period_transactions)
        period_volume = sum(float(t.amount) for t in period_transactions if t.amount)
        
        # Winning vs losing positions
        winning_positions = 0
        losing_positions = 0
        for position in account.positions:
            if position.is_active and position.unrealized_pnl:
                if float(position.unrealized_pnl) > 0:
                    winning_positions += 1
                else:
                    losing_positions += 1
        
        win_rate = (winning_positions / (winning_positions + losing_positions)) * 100 if (winning_positions + losing_positions) > 0 else 0
        
        performance_analysis = {
            "account_name": account.name,
            "analysis_period": period,
            "current_value": total_value,
            "initial_capital": initial_capital,
            "total_return": total_return,
            "total_return_percentage": total_return_pct,
            "period_trades": period_trades,
            "period_volume": period_volume,
            "winning_positions": winning_positions,
            "losing_positions": losing_positions,
            "win_rate": win_rate,
            "cash_position": float(account.current_cash),
            "invested_amount": total_value - float(account.current_cash),
            "benchmark": benchmark,
            "note": f"Benchmark comparison would require {benchmark} data in production system"
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=performance_analysis,
            message=f"Completed performance analysis for {period} period"
        )
    
    async def _analyze_allocation(self, session, account: Account) -> ToolResult:
        """Analyze current portfolio allocation"""
        # Get active positions
        result = await session.execute(
            select(Position).where(
                and_(Position.account_id == account.id, Position.is_active == True)
            )
        )
        positions = result.scalars().all()
        
        total_invested = sum(float(p.market_value) for p in positions if p.market_value)
        total_value = float(account.current_cash) + total_invested
        
        # Allocation by position
        position_allocation = []
        for position in positions:
            if position.market_value:
                weight = (float(position.market_value) / total_value) * 100 if total_value > 0 else 0
                position_allocation.append({
                    "symbol": position.stock_symbol,
                    "value": float(position.market_value),
                    "weight": weight,
                    "unrealized_pnl": float(position.unrealized_pnl) if position.unrealized_pnl else 0
                })
        
        # Allocation by sector
        sector_allocation = {}
        for position in positions:
            if not position.market_value:
                continue
                
            # Get stock sector
            stock_result = await session.execute(
                select(Stock).where(Stock.symbol == position.stock_symbol)
            )
            stock = stock_result.scalar_one_or_none()
            
            if stock and stock.sector:
                sector = stock.sector
                if sector not in sector_allocation:
                    sector_allocation[sector] = 0
                sector_allocation[sector] += float(position.market_value)
        
        # Convert to percentages
        sector_percentages = {}
        for sector, value in sector_allocation.items():
            sector_percentages[sector] = (value / total_value) * 100 if total_value > 0 else 0
        
        allocation_analysis = {
            "total_portfolio_value": total_value,
            "cash_allocation": (float(account.current_cash) / total_value) * 100 if total_value > 0 else 0,
            "invested_allocation": (total_invested / total_value) * 100 if total_value > 0 else 0,
            "number_of_positions": len(positions),
            "position_allocation": sorted(position_allocation, key=lambda x: x["weight"], reverse=True),
            "sector_allocation": sector_percentages,
            "largest_position": max(position_allocation, key=lambda x: x["weight"])["weight"] if position_allocation else 0,
            "allocation_score": self._calculate_allocation_score(position_allocation, sector_percentages)
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=allocation_analysis,
            message="Completed portfolio allocation analysis"
        )
    
    async def _analyze_rebalancing_needs(self, session, account: Account) -> ToolResult:
        """Analyze portfolio rebalancing needs"""
        # Get current allocation
        allocation_result = await self._analyze_allocation(session, account)
        allocation_data = allocation_result.data
        
        # Define target allocation (this would be configurable in production)
        target_cash_allocation = 10  # 10% cash
        max_position_weight = 15     # Max 15% per position
        max_sector_weight = 30       # Max 30% per sector
        
        rebalancing_needs = []
        
        # Check cash allocation
        current_cash_pct = allocation_data["cash_allocation"]
        if current_cash_pct < target_cash_allocation - 5:
            rebalancing_needs.append({
                "type": "INCREASE_CASH",
                "current": current_cash_pct,
                "target": target_cash_allocation,
                "action": "Consider selling some positions to increase cash reserves"
            })
        elif current_cash_pct > target_cash_allocation + 10:
            rebalancing_needs.append({
                "type": "DEPLOY_CASH",
                "current": current_cash_pct,
                "target": target_cash_allocation,
                "action": "Consider investing excess cash"
            })
        
        # Check position concentration
        for position in allocation_data["position_allocation"]:
            if position["weight"] > max_position_weight:
                rebalancing_needs.append({
                    "type": "REDUCE_POSITION",
                    "symbol": position["symbol"],
                    "current": position["weight"],
                    "target": max_position_weight,
                    "action": f"Consider reducing {position['symbol']} position"
                })
        
        # Check sector concentration
        for sector, weight in allocation_data["sector_allocation"].items():
            if weight > max_sector_weight:
                rebalancing_needs.append({
                    "type": "REDUCE_SECTOR",
                    "sector": sector,
                    "current": weight,
                    "target": max_sector_weight,
                    "action": f"Consider reducing exposure to {sector} sector"
                })
        
        rebalancing_analysis = {
            "needs_rebalancing": len(rebalancing_needs) > 0,
            "rebalancing_actions": rebalancing_needs,
            "current_allocation": allocation_data,
            "target_parameters": {
                "target_cash_allocation": target_cash_allocation,
                "max_position_weight": max_position_weight,
                "max_sector_weight": max_sector_weight
            },
            "priority": "HIGH" if len(rebalancing_needs) > 3 else "MEDIUM" if len(rebalancing_needs) > 1 else "LOW"
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=rebalancing_analysis,
            message=f"Identified {len(rebalancing_needs)} rebalancing opportunities"
        )
    
    async def _suggest_optimization(self, session, account: Account) -> ToolResult:
        """Suggest portfolio optimization opportunities"""
        # Get current portfolio data
        allocation_result = await self._analyze_allocation(session, account)
        allocation_data = allocation_result.data
        
        risk_result = await session.execute(
            select(Position).where(
                and_(Position.account_id == account.id, Position.is_active == True)
            )
        )
        positions = risk_result.scalars().all()
        
        optimization_suggestions = []
        
        # Diversification suggestions
        num_positions = len(positions)
        if num_positions < 5:
            optimization_suggestions.append({
                "category": "DIVERSIFICATION",
                "priority": "HIGH",
                "suggestion": "Consider adding more positions to improve diversification",
                "current_positions": num_positions,
                "target_positions": "8-12"
            })
        elif num_positions > 20:
            optimization_suggestions.append({
                "category": "SIMPLIFICATION",
                "priority": "MEDIUM",
                "suggestion": "Consider consolidating positions to reduce complexity",
                "current_positions": num_positions,
                "target_positions": "10-15"
            })
        
        # Sector diversification
        num_sectors = len(allocation_data["sector_allocation"])
        if num_sectors < 3:
            optimization_suggestions.append({
                "category": "SECTOR_DIVERSIFICATION",
                "priority": "HIGH",
                "suggestion": "Consider diversifying across more sectors",
                "current_sectors": num_sectors,
                "target_sectors": "5-8"
            })
        
        # Cash optimization
        cash_pct = allocation_data["cash_allocation"]
        if cash_pct > 20:
            optimization_suggestions.append({
                "category": "CASH_DEPLOYMENT",
                "priority": "MEDIUM",
                "suggestion": "Consider investing excess cash for better returns",
                "current_cash_pct": cash_pct,
                "optimal_cash_pct": "5-15"
            })
        
        # Performance optimization
        losing_positions = [p for p in positions if p.unrealized_pnl and float(p.unrealized_pnl) < -1000]
        if losing_positions:
            optimization_suggestions.append({
                "category": "LOSS_MANAGEMENT",
                "priority": "HIGH",
                "suggestion": f"Review {len(losing_positions)} positions with significant losses",
                "positions_to_review": [p.stock_symbol for p in losing_positions]
            })
        
        optimization_analysis = {
            "optimization_score": self._calculate_optimization_score(allocation_data, positions),
            "suggestions": optimization_suggestions,
            "current_metrics": {
                "positions": num_positions,
                "sectors": num_sectors,
                "cash_percentage": cash_pct,
                "largest_position": allocation_data["largest_position"]
            },
            "priority_actions": [s for s in optimization_suggestions if s["priority"] == "HIGH"]
        }
        
        return ToolResult(
            status=ToolStatus.SUCCESS,
            data=optimization_analysis,
            message=f"Generated {len(optimization_suggestions)} optimization suggestions"
        )
    
    def _calculate_allocation_score(self, position_allocation: List[Dict], sector_allocation: Dict) -> int:
        """Calculate allocation quality score (0-100)"""
        score = 100
        
        # Penalize concentration
        if position_allocation:
            max_position = max(p["weight"] for p in position_allocation)
            if max_position > 20:
                score -= 20
            elif max_position > 15:
                score -= 10
        
        if sector_allocation:
            max_sector = max(sector_allocation.values())
            if max_sector > 40:
                score -= 20
            elif max_sector > 30:
                score -= 10
        
        # Reward diversification
        num_positions = len(position_allocation)
        if num_positions >= 8:
            score += 10
        elif num_positions < 5:
            score -= 15
        
        num_sectors = len(sector_allocation)
        if num_sectors >= 5:
            score += 10
        elif num_sectors < 3:
            score -= 15
        
        return max(0, min(100, score))
    
    def _calculate_optimization_score(self, allocation_data: Dict, positions: List) -> int:
        """Calculate portfolio optimization score (0-100)"""
        score = 70  # Base score
        
        # Factor in allocation score
        allocation_score = allocation_data.get("allocation_score", 50)
        score = (score + allocation_score) / 2
        
        # Adjust for performance
        winning_positions = sum(1 for p in positions if p.unrealized_pnl and float(p.unrealized_pnl) > 0)
        total_positions = len(positions)
        
        if total_positions > 0:
            win_rate = winning_positions / total_positions
            if win_rate > 0.6:
                score += 10
            elif win_rate < 0.4:
                score -= 10
        
        # Adjust for cash allocation
        cash_pct = allocation_data["cash_allocation"]
        if 5 <= cash_pct <= 15:
            score += 5
        elif cash_pct > 25 or cash_pct < 2:
            score -= 10
        
        return max(0, min(100, int(score)))
