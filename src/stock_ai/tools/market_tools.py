"""
Market data and analysis tools for Stock AI Agents system
"""

import asyncio
from decimal import Decimal
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json

from sqlalchemy import select, and_, desc
from sqlalchemy.orm import selectinload

from .base import BaseTool, ToolResult, ToolStatus, ToolDefinition, ToolParameter, ToolError
from ..database import get_db_session
from ..models import Stock, MarketData, TradingSession
from ..models.market import MarketStatus


class MarketDataTool(BaseTool):
    """Tool for retrieving market data and stock prices"""
    
    def __init__(self):
        super().__init__(
            name="market_data_tool",
            description="Retrieve real-time and historical market data for stocks"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="action",
                    type="string",
                    description="Action to perform",
                    enum=["get_current_price", "get_historical_data", "get_market_status", "get_stock_info"]
                ),
                ToolParameter(
                    name="symbol",
                    type="string",
                    description="Stock symbol (required for stock-specific actions)",
                    required=False
                ),
                ToolParameter(
                    name="period",
                    type="string",
                    description="Time period for historical data",
                    enum=["1d", "5d", "1mo", "3mo", "6mo", "1y", "2y", "5y"],
                    default="1mo",
                    required=False
                ),
                ToolParameter(
                    name="interval",
                    type="string",
                    description="Data interval",
                    enum=["1m", "5m", "15m", "30m", "1h", "1d"],
                    default="1d",
                    required=False
                ),
            ]
        )
    
    async def execute(self, action: str, symbol: str = None, period: str = "1mo", 
                     interval: str = "1d") -> ToolResult:
        """Execute market data action"""
        
        try:
            if action == "get_current_price":
                if not symbol:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error="Symbol is required for get_current_price action"
                    )
                return await self._get_current_price(symbol)
            elif action == "get_historical_data":
                if not symbol:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error="Symbol is required for get_historical_data action"
                    )
                return await self._get_historical_data(symbol, period, interval)
            elif action == "get_market_status":
                return await self._get_market_status()
            elif action == "get_stock_info":
                if not symbol:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error="Symbol is required for get_stock_info action"
                    )
                return await self._get_stock_info(symbol)
            else:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Unknown action: {action}"
                )
                
        except Exception as e:
            raise ToolError(f"Market data tool execution failed: {str(e)}")
    
    async def _get_current_price(self, symbol: str) -> ToolResult:
        """Get current stock price"""
        async for session in get_db_session():
            # Get stock
            result = await session.execute(
                select(Stock).where(Stock.symbol == symbol.upper())
            )
            stock = result.scalar_one_or_none()
            
            if not stock:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Stock '{symbol}' not found"
                )
            
            # Get latest market data
            result = await session.execute(
                select(MarketData)
                .where(MarketData.stock_id == stock.id)
                .order_by(desc(MarketData.timestamp))
                .limit(1)
            )
            latest_data = result.scalar_one_or_none()
            
            if not latest_data:
                # In a real implementation, this would fetch from external API
                return ToolResult(
                    status=ToolStatus.WARNING,
                    message=f"No market data available for '{symbol}'. In production, this would fetch from external API.",
                    data={
                        "symbol": symbol,
                        "price": None,
                        "timestamp": None,
                        "note": "Would fetch from external market data provider"
                    }
                )
            
            price_data = {
                "symbol": symbol,
                "current_price": float(latest_data.close_price),
                "open_price": float(latest_data.open_price),
                "high_price": float(latest_data.high_price),
                "low_price": float(latest_data.low_price),
                "volume": int(latest_data.volume),
                "timestamp": latest_data.timestamp.isoformat(),
                "data_source": latest_data.data_source,
            }
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data=price_data,
                message=f"Retrieved current price for '{symbol}'"
            )
    
    async def _get_historical_data(self, symbol: str, period: str, interval: str) -> ToolResult:
        """Get historical market data"""
        async for session in get_db_session():
            # Get stock
            result = await session.execute(
                select(Stock).where(Stock.symbol == symbol.upper())
            )
            stock = result.scalar_one_or_none()
            
            if not stock:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Stock '{symbol}' not found"
                )
            
            # Calculate date range based on period
            end_date = datetime.now()
            if period == "1d":
                start_date = end_date - timedelta(days=1)
            elif period == "5d":
                start_date = end_date - timedelta(days=5)
            elif period == "1mo":
                start_date = end_date - timedelta(days=30)
            elif period == "3mo":
                start_date = end_date - timedelta(days=90)
            elif period == "6mo":
                start_date = end_date - timedelta(days=180)
            elif period == "1y":
                start_date = end_date - timedelta(days=365)
            elif period == "2y":
                start_date = end_date - timedelta(days=730)
            elif period == "5y":
                start_date = end_date - timedelta(days=1825)
            else:
                start_date = end_date - timedelta(days=30)
            
            # Get historical data
            result = await session.execute(
                select(MarketData)
                .where(
                    and_(
                        MarketData.stock_id == stock.id,
                        MarketData.timestamp >= start_date,
                        MarketData.timestamp <= end_date
                    )
                )
                .order_by(MarketData.timestamp)
            )
            historical_data = result.scalars().all()
            
            if not historical_data:
                return ToolResult(
                    status=ToolStatus.WARNING,
                    message=f"No historical data available for '{symbol}' in the specified period. In production, this would fetch from external API.",
                    data={
                        "symbol": symbol,
                        "period": period,
                        "interval": interval,
                        "data": [],
                        "note": "Would fetch from external market data provider"
                    }
                )
            
            data_points = []
            for data_point in historical_data:
                data_points.append({
                    "timestamp": data_point.timestamp.isoformat(),
                    "open": float(data_point.open_price),
                    "high": float(data_point.high_price),
                    "low": float(data_point.low_price),
                    "close": float(data_point.close_price),
                    "volume": int(data_point.volume),
                    "adjusted_close": float(data_point.adjusted_close) if data_point.adjusted_close else None,
                })
            
            historical_result = {
                "symbol": symbol,
                "period": period,
                "interval": interval,
                "data_points": len(data_points),
                "data": data_points,
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
            }
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data=historical_result,
                message=f"Retrieved {len(data_points)} historical data points for '{symbol}'"
            )
    
    async def _get_market_status(self) -> ToolResult:
        """Get current market status"""
        async for session in get_db_session():
            # Get today's trading session
            today = datetime.now().date()
            result = await session.execute(
                select(TradingSession).where(
                    and_(
                        TradingSession.date >= datetime.combine(today, datetime.min.time()),
                        TradingSession.date < datetime.combine(today, datetime.max.time().replace(microsecond=0))
                    )
                )
            )
            session_data = result.scalar_one_or_none()
            
            current_time = datetime.now()
            
            if not session_data:
                # Create default session info
                market_status = {
                    "status": "UNKNOWN",
                    "is_open": False,
                    "current_time": current_time.isoformat(),
                    "next_open": None,
                    "next_close": None,
                    "note": "No trading session data available"
                }
            else:
                # Determine current market status
                is_open = (session_data.market_open <= current_time <= session_data.market_close)
                
                market_status = {
                    "status": session_data.market_status.value,
                    "is_open": is_open,
                    "current_time": current_time.isoformat(),
                    "market_open": session_data.market_open.isoformat(),
                    "market_close": session_data.market_close.isoformat(),
                    "pre_market_start": session_data.pre_market_start.isoformat() if session_data.pre_market_start else None,
                    "after_hours_end": session_data.after_hours_end.isoformat() if session_data.after_hours_end else None,
                    "notes": session_data.notes,
                }
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data=market_status,
                message="Retrieved current market status"
            )
    
    async def _get_stock_info(self, symbol: str) -> ToolResult:
        """Get stock information"""
        async for session in get_db_session():
            result = await session.execute(
                select(Stock).where(Stock.symbol == symbol.upper())
            )
            stock = result.scalar_one_or_none()
            
            if not stock:
                return ToolResult(
                    status=ToolStatus.ERROR,
                    error=f"Stock '{symbol}' not found"
                )
            
            stock_info = {
                "symbol": stock.symbol,
                "name": stock.name,
                "exchange": stock.exchange,
                "sector": stock.sector,
                "industry": stock.industry,
                "market_cap": float(stock.market_cap) if stock.market_cap else None,
                "shares_outstanding": int(stock.shares_outstanding) if stock.shares_outstanding else None,
                "currency": stock.currency,
                "is_active": stock.is_active,
                "is_tradable": stock.is_tradable,
                "description": stock.description,
                "website": stock.website,
            }
            
            return ToolResult(
                status=ToolStatus.SUCCESS,
                data=stock_info,
                message=f"Retrieved information for '{symbol}'"
            )


class StockScreenerTool(BaseTool):
    """Tool for screening and filtering stocks"""
    
    def __init__(self):
        super().__init__(
            name="stock_screener_tool",
            description="Screen and filter stocks based on various criteria"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="criteria",
                    type="object",
                    description="Screening criteria (sector, market_cap_min, market_cap_max, etc.)",
                    default={}
                ),
                ToolParameter(
                    name="limit",
                    type="number",
                    description="Maximum number of stocks to return",
                    default=50,
                    minimum=1,
                    maximum=500
                ),
                ToolParameter(
                    name="sort_by",
                    type="string",
                    description="Field to sort by",
                    enum=["market_cap", "symbol", "name"],
                    default="market_cap"
                ),
                ToolParameter(
                    name="sort_order",
                    type="string",
                    description="Sort order",
                    enum=["asc", "desc"],
                    default="desc"
                ),
            ]
        )
    
    async def execute(self, criteria: Dict = None, limit: int = 50, 
                     sort_by: str = "market_cap", sort_order: str = "desc") -> ToolResult:
        """Execute stock screening"""
        
        if criteria is None:
            criteria = {}
        
        async for session in get_db_session():
            try:
                # Build query
                query = select(Stock).where(Stock.is_active == True)
                
                # Apply filters
                if "sector" in criteria:
                    query = query.where(Stock.sector == criteria["sector"])
                
                if "industry" in criteria:
                    query = query.where(Stock.industry == criteria["industry"])
                
                if "exchange" in criteria:
                    query = query.where(Stock.exchange == criteria["exchange"])
                
                if "market_cap_min" in criteria:
                    query = query.where(Stock.market_cap >= Decimal(str(criteria["market_cap_min"])))
                
                if "market_cap_max" in criteria:
                    query = query.where(Stock.market_cap <= Decimal(str(criteria["market_cap_max"])))
                
                # Apply sorting
                if sort_by == "market_cap":
                    if sort_order == "desc":
                        query = query.order_by(desc(Stock.market_cap))
                    else:
                        query = query.order_by(Stock.market_cap)
                elif sort_by == "symbol":
                    if sort_order == "desc":
                        query = query.order_by(desc(Stock.symbol))
                    else:
                        query = query.order_by(Stock.symbol)
                elif sort_by == "name":
                    if sort_order == "desc":
                        query = query.order_by(desc(Stock.name))
                    else:
                        query = query.order_by(Stock.name)
                
                # Apply limit
                query = query.limit(limit)
                
                # Execute query
                result = await session.execute(query)
                stocks = result.scalars().all()
                
                # Format results
                screened_stocks = []
                for stock in stocks:
                    stock_data = {
                        "symbol": stock.symbol,
                        "name": stock.name,
                        "exchange": stock.exchange,
                        "sector": stock.sector,
                        "industry": stock.industry,
                        "market_cap": float(stock.market_cap) if stock.market_cap else None,
                        "currency": stock.currency,
                        "is_tradable": stock.is_tradable,
                    }
                    screened_stocks.append(stock_data)
                
                return ToolResult(
                    status=ToolStatus.SUCCESS,
                    data={
                        "criteria": criteria,
                        "total_results": len(screened_stocks),
                        "stocks": screened_stocks,
                        "sort_by": sort_by,
                        "sort_order": sort_order,
                    },
                    message=f"Screened {len(screened_stocks)} stocks matching criteria"
                )
                
            except Exception as e:
                raise ToolError(f"Stock screening failed: {str(e)}")


class TechnicalAnalysisTool(BaseTool):
    """Tool for technical analysis of stocks"""
    
    def __init__(self):
        super().__init__(
            name="technical_analysis_tool",
            description="Perform technical analysis on stocks using various indicators"
        )
    
    def _create_definition(self) -> ToolDefinition:
        return ToolDefinition(
            name=self.name,
            description=self.description,
            parameters=[
                ToolParameter(
                    name="symbol",
                    type="string",
                    description="Stock symbol to analyze"
                ),
                ToolParameter(
                    name="indicators",
                    type="array",
                    description="Technical indicators to calculate",
                    default=["sma_20", "sma_50", "rsi"]
                ),
                ToolParameter(
                    name="period",
                    type="string",
                    description="Time period for analysis",
                    enum=["1mo", "3mo", "6mo", "1y"],
                    default="3mo"
                ),
            ]
        )
    
    async def execute(self, symbol: str, indicators: List[str] = None, 
                     period: str = "3mo") -> ToolResult:
        """Execute technical analysis"""
        
        if indicators is None:
            indicators = ["sma_20", "sma_50", "rsi"]
        
        async for session in get_db_session():
            try:
                # Get stock
                result = await session.execute(
                    select(Stock).where(Stock.symbol == symbol.upper())
                )
                stock = result.scalar_one_or_none()
                
                if not stock:
                    return ToolResult(
                        status=ToolStatus.ERROR,
                        error=f"Stock '{symbol}' not found"
                    )
                
                # Get historical data for analysis
                end_date = datetime.now()
                if period == "1mo":
                    start_date = end_date - timedelta(days=30)
                elif period == "3mo":
                    start_date = end_date - timedelta(days=90)
                elif period == "6mo":
                    start_date = end_date - timedelta(days=180)
                elif period == "1y":
                    start_date = end_date - timedelta(days=365)
                else:
                    start_date = end_date - timedelta(days=90)
                
                result = await session.execute(
                    select(MarketData)
                    .where(
                        and_(
                            MarketData.stock_id == stock.id,
                            MarketData.timestamp >= start_date
                        )
                    )
                    .order_by(MarketData.timestamp)
                )
                historical_data = result.scalars().all()
                
                if len(historical_data) < 20:
                    return ToolResult(
                        status=ToolStatus.WARNING,
                        message=f"Insufficient data for technical analysis of '{symbol}'. Need at least 20 data points.",
                        data={
                            "symbol": symbol,
                            "data_points": len(historical_data),
                            "note": "In production, this would fetch more data from external API"
                        }
                    )
                
                # Calculate technical indicators
                analysis_results = {
                    "symbol": symbol,
                    "period": period,
                    "data_points": len(historical_data),
                    "indicators": {},
                    "analysis_date": datetime.now().isoformat(),
                }
                
                # Extract price data
                prices = [float(data.close_price) for data in historical_data]
                
                # Calculate indicators
                for indicator in indicators:
                    if indicator == "sma_20" and len(prices) >= 20:
                        sma_20 = sum(prices[-20:]) / 20
                        analysis_results["indicators"]["sma_20"] = round(sma_20, 2)
                    
                    elif indicator == "sma_50" and len(prices) >= 50:
                        sma_50 = sum(prices[-50:]) / 50
                        analysis_results["indicators"]["sma_50"] = round(sma_50, 2)
                    
                    elif indicator == "rsi" and len(prices) >= 14:
                        # Simplified RSI calculation
                        gains = []
                        losses = []
                        for i in range(1, min(15, len(prices))):
                            change = prices[-i] - prices[-i-1]
                            if change > 0:
                                gains.append(change)
                                losses.append(0)
                            else:
                                gains.append(0)
                                losses.append(abs(change))
                        
                        avg_gain = sum(gains) / len(gains) if gains else 0
                        avg_loss = sum(losses) / len(losses) if losses else 0
                        
                        if avg_loss == 0:
                            rsi = 100
                        else:
                            rs = avg_gain / avg_loss
                            rsi = 100 - (100 / (1 + rs))
                        
                        analysis_results["indicators"]["rsi"] = round(rsi, 2)
                
                # Add current price and basic analysis
                current_price = prices[-1]
                analysis_results["current_price"] = current_price
                
                # Simple trend analysis
                if "sma_20" in analysis_results["indicators"] and "sma_50" in analysis_results["indicators"]:
                    sma_20 = analysis_results["indicators"]["sma_20"]
                    sma_50 = analysis_results["indicators"]["sma_50"]
                    
                    if current_price > sma_20 > sma_50:
                        trend = "BULLISH"
                    elif current_price < sma_20 < sma_50:
                        trend = "BEARISH"
                    else:
                        trend = "NEUTRAL"
                    
                    analysis_results["trend"] = trend
                
                return ToolResult(
                    status=ToolStatus.SUCCESS,
                    data=analysis_results,
                    message=f"Completed technical analysis for '{symbol}'"
                )
                
            except Exception as e:
                raise ToolError(f"Technical analysis failed: {str(e)}")
