"""
Scheduler Service - Manages market hours and system pause/resume functionality
"""

import asyncio
from datetime import datetime, time, timezone
from typing import Dict, Any, Optional
from enum import Enum

from loguru import logger

from ..config import settings
from ..tools import AlertTool, NotificationTool


class MarketState(str, Enum):
    """Market states"""
    PRE_MARKET = "PRE_MARKET"
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    AFTER_HOURS = "AFTER_HOURS"
    HOLIDAY = "HOLIDAY"


class SystemState(str, Enum):
    """System states"""
    RUNNING = "RUNNING"
    PAUSED = "PAUSED"
    MARKET_CLOSED_PAUSE = "MARKET_CLOSED_PAUSE"
    MANUAL_PAUSE = "MANUAL_PAUSE"
    EMERGENCY_STOP = "EMERGENCY_STOP"


class SchedulerService:
    """Service for managing system scheduling and market hours"""
    
    def __init__(self, agent_manager, coordination_service):
        self.agent_manager = agent_manager
        self.coordination_service = coordination_service
        self.system_state = SystemState.PAUSED
        self.market_state = MarketState.CLOSED
        self.is_running = False
        self.scheduler_task = None
        self.manual_pause = False
        self.emergency_stop = False
        
        # Market hours (UTC)
        self.market_open_time = time(
            hour=settings.market_hours.open_hour,
            minute=settings.market_hours.open_minute,
            tzinfo=timezone.utc
        )
        self.market_close_time = time(
            hour=settings.market_hours.close_hour,
            minute=settings.market_hours.close_minute,
            tzinfo=timezone.utc
        )
        
        # Tools
        self.alert_tool = AlertTool()
        self.notification_tool = NotificationTool()
    
    async def start(self) -> None:
        """Start scheduler service"""
        if self.is_running:
            logger.warning("Scheduler service is already running")
            return
        
        try:
            logger.info("Starting Scheduler Service")
            self.is_running = True
            
            # Start scheduler loop
            self.scheduler_task = asyncio.create_task(
                self._scheduler_loop(),
                name="scheduler_service"
            )
            
            logger.info("Scheduler Service started")
            
        except Exception as e:
            logger.error(f"Failed to start Scheduler Service: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop scheduler service"""
        if not self.is_running:
            logger.warning("Scheduler service is not running")
            return
        
        try:
            logger.info("Stopping Scheduler Service")
            self.is_running = False
            
            if self.scheduler_task and not self.scheduler_task.done():
                self.scheduler_task.cancel()
                try:
                    await self.scheduler_task
                except asyncio.CancelledError:
                    logger.info("Scheduler task cancelled")
            
            logger.info("Scheduler Service stopped")
            
        except Exception as e:
            logger.error(f"Error stopping Scheduler Service: {e}")
    
    async def _scheduler_loop(self) -> None:
        """Main scheduler loop"""
        while self.is_running:
            try:
                # Update market state
                await self._update_market_state()
                
                # Update system state based on market and manual controls
                await self._update_system_state()
                
                # Apply system state to agents
                await self._apply_system_state()
                
                # Wait before next check
                await asyncio.sleep(60)  # Check every minute
                
            except asyncio.CancelledError:
                logger.info("Scheduler loop cancelled")
                break
                
            except Exception as e:
                logger.error(f"Error in scheduler loop: {e}")
                await asyncio.sleep(30)  # Wait before retrying
    
    async def _update_market_state(self) -> None:
        """Update current market state"""
        now = datetime.now(timezone.utc).time()
        
        # Simple market hours logic (would be more complex in production)
        if self.market_open_time <= now <= self.market_close_time:
            new_state = MarketState.OPEN
        else:
            new_state = MarketState.CLOSED
        
        # Check if state changed
        if new_state != self.market_state:
            old_state = self.market_state
            self.market_state = new_state
            
            logger.info(f"Market state changed: {old_state} -> {new_state}")
            
            # Send notification
            await self.notification_tool.safe_execute(
                notification_type="system_status",
                title=f"Market State Change",
                message=f"Market is now {new_state.value}",
                data={
                    "previous_state": old_state.value,
                    "new_state": new_state.value,
                    "timestamp": datetime.now().isoformat()
                }
            )
    
    async def _update_system_state(self) -> None:
        """Update system state based on market and controls"""
        new_state = self.system_state
        
        # Emergency stop takes precedence
        if self.emergency_stop:
            new_state = SystemState.EMERGENCY_STOP
        
        # Manual pause takes precedence over market hours
        elif self.manual_pause:
            new_state = SystemState.MANUAL_PAUSE
        
        # Market-based state
        elif self.market_state == MarketState.CLOSED:
            new_state = SystemState.MARKET_CLOSED_PAUSE
        
        elif self.market_state == MarketState.OPEN:
            new_state = SystemState.RUNNING
        
        # Check if state changed
        if new_state != self.system_state:
            old_state = self.system_state
            self.system_state = new_state
            
            logger.info(f"System state changed: {old_state} -> {new_state}")
            
            # Send alert for important state changes
            if new_state == SystemState.EMERGENCY_STOP:
                await self.alert_tool.safe_execute(
                    alert_type="system_alert",
                    level="CRITICAL",
                    title="Emergency Stop Activated",
                    message="System has been emergency stopped",
                    data={"previous_state": old_state.value}
                )
            
            elif new_state == SystemState.RUNNING and old_state != SystemState.RUNNING:
                await self.alert_tool.safe_execute(
                    alert_type="system_alert",
                    level="INFO",
                    title="System Resumed",
                    message="Trading system has resumed operation",
                    data={"previous_state": old_state.value}
                )
    
    async def _apply_system_state(self) -> None:
        """Apply current system state to agents"""
        try:
            if self.system_state == SystemState.RUNNING:
                # Resume all agents
                for agent_name in self.agent_manager.agents:
                    agent = self.agent_manager.get_agent(agent_name)
                    if agent and agent.status.value == "PAUSED":
                        await agent.resume()
                
                # Resume coordination service
                if hasattr(self.coordination_service, 'is_running') and not self.coordination_service.is_running:
                    await self.coordination_service.start()
            
            elif self.system_state in [SystemState.PAUSED, SystemState.MARKET_CLOSED_PAUSE, 
                                     SystemState.MANUAL_PAUSE, SystemState.EMERGENCY_STOP]:
                # Pause all agents
                for agent_name in self.agent_manager.agents:
                    agent = self.agent_manager.get_agent(agent_name)
                    if agent and agent.status.value == "ACTIVE":
                        await agent.pause()
                
                # Pause coordination service
                if hasattr(self.coordination_service, 'is_running') and self.coordination_service.is_running:
                    await self.coordination_service.stop()
        
        except Exception as e:
            logger.error(f"Error applying system state: {e}")
    
    async def pause_system(self, reason: str = "Manual pause") -> bool:
        """Manually pause the system"""
        try:
            logger.info(f"Pausing system: {reason}")
            self.manual_pause = True
            
            await self.notification_tool.safe_execute(
                notification_type="system_status",
                title="System Paused",
                message=f"System paused: {reason}",
                data={"reason": reason, "timestamp": datetime.now().isoformat()}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to pause system: {e}")
            return False
    
    async def resume_system(self, reason: str = "Manual resume") -> bool:
        """Manually resume the system"""
        try:
            logger.info(f"Resuming system: {reason}")
            self.manual_pause = False
            self.emergency_stop = False
            
            await self.notification_tool.safe_execute(
                notification_type="system_status",
                title="System Resumed",
                message=f"System resumed: {reason}",
                data={"reason": reason, "timestamp": datetime.now().isoformat()}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to resume system: {e}")
            return False
    
    async def emergency_stop_system(self, reason: str = "Emergency stop") -> bool:
        """Emergency stop the system"""
        try:
            logger.critical(f"Emergency stopping system: {reason}")
            self.emergency_stop = True
            
            await self.alert_tool.safe_execute(
                alert_type="system_alert",
                level="CRITICAL",
                title="Emergency Stop",
                message=f"Emergency stop activated: {reason}",
                data={"reason": reason, "timestamp": datetime.now().isoformat()}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to emergency stop system: {e}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """Get scheduler status"""
        now = datetime.now(timezone.utc)
        
        return {
            "is_running": self.is_running,
            "system_state": self.system_state.value,
            "market_state": self.market_state.value,
            "manual_pause": self.manual_pause,
            "emergency_stop": self.emergency_stop,
            "current_time": now.isoformat(),
            "market_open_time": self.market_open_time.isoformat(),
            "market_close_time": self.market_close_time.isoformat(),
            "is_market_hours": self.market_state == MarketState.OPEN,
            "next_market_event": self._get_next_market_event(now),
        }
    
    def _get_next_market_event(self, now: datetime) -> Dict[str, Any]:
        """Get next market event (open/close)"""
        now_time = now.time()
        
        if now_time < self.market_open_time:
            # Market opens today
            next_event = "MARKET_OPEN"
            next_time = datetime.combine(now.date(), self.market_open_time)
        elif now_time < self.market_close_time:
            # Market closes today
            next_event = "MARKET_CLOSE"
            next_time = datetime.combine(now.date(), self.market_close_time)
        else:
            # Market opens tomorrow
            next_event = "MARKET_OPEN"
            tomorrow = now.date().replace(day=now.date().day + 1)
            next_time = datetime.combine(tomorrow, self.market_open_time)
        
        time_until = next_time - now.replace(tzinfo=None)
        
        return {
            "event": next_event,
            "time": next_time.isoformat(),
            "seconds_until": int(time_until.total_seconds()),
            "human_readable": self._format_time_until(time_until.total_seconds())
        }
    
    def _format_time_until(self, seconds: float) -> str:
        """Format time until next event"""
        if seconds < 60:
            return f"{int(seconds)}s"
        elif seconds < 3600:
            return f"{int(seconds // 60)}m {int(seconds % 60)}s"
        else:
            hours = int(seconds // 3600)
            minutes = int((seconds % 3600) // 60)
            return f"{hours}h {minutes}m"
