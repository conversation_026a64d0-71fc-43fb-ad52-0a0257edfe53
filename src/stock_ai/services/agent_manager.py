"""
Agent Manager - Manages lifecycle and coordination of all AI agents
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime

from loguru import logger

from ..agents import (
    BaseAgent, MarketAnalystAgent, StockSelectorAgent, 
    TradingDecisionMakerAgent, PortfolioManagerAgent, RiskControllerAgent
)
from ..models.agent import AgentStatus
from ..database import get_db_session
from ..models import Agent as AgentModel


class AgentManager:
    """Manages all AI agents in the system"""
    
    def __init__(self):
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_tasks: Dict[str, asyncio.Task] = {}
        self.is_running = False
        self.coordination_enabled = True
        
    async def initialize(self) -> None:
        """Initialize all agents"""
        try:
            logger.info("Initializing Agent Manager")
            
            # Create agent instances
            self.agents = {
                "market_analyst": MarketAnalystAgent(),
                "stock_selector": StockSelectorAgent(),
                "trading_decision_maker": TradingDecisionMakerAgent(),
                "portfolio_manager": PortfolioManagerAgent(),
                "risk_controller": RiskControllerAgent(),
            }
            
            # Initialize each agent
            for name, agent in self.agents.items():
                await agent.start()
                logger.info(f"Initialized agent: {name}")
            
            logger.info("Agent Manager initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Agent Manager: {e}")
            raise
    
    async def start_all_agents(self) -> None:
        """Start all agents in continuous mode"""
        if self.is_running:
            logger.warning("Agents are already running")
            return
        
        try:
            logger.info("Starting all agents")
            self.is_running = True
            
            # Start each agent in its own task
            for name, agent in self.agents.items():
                if agent.config.enabled:
                    task = asyncio.create_task(
                        self._run_agent_with_error_handling(agent),
                        name=f"agent_{name}"
                    )
                    self.agent_tasks[name] = task
                    logger.info(f"Started agent task: {name}")
            
            logger.info(f"Started {len(self.agent_tasks)} agent tasks")
            
        except Exception as e:
            logger.error(f"Failed to start agents: {e}")
            await self.stop_all_agents()
            raise
    
    async def stop_all_agents(self) -> None:
        """Stop all agents"""
        if not self.is_running:
            logger.warning("Agents are not running")
            return
        
        try:
            logger.info("Stopping all agents")
            self.is_running = False
            
            # Cancel all agent tasks
            for name, task in self.agent_tasks.items():
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        logger.info(f"Cancelled agent task: {name}")
                    except Exception as e:
                        logger.error(f"Error stopping agent {name}: {e}")
            
            # Stop all agents
            for name, agent in self.agents.items():
                await agent.stop()
                logger.info(f"Stopped agent: {name}")
            
            self.agent_tasks.clear()
            logger.info("All agents stopped")
            
        except Exception as e:
            logger.error(f"Error stopping agents: {e}")
    
    async def _run_agent_with_error_handling(self, agent: BaseAgent) -> None:
        """Run agent with error handling and recovery"""
        agent_name = agent.name
        
        try:
            logger.info(f"Starting continuous execution for agent: {agent_name}")
            await agent.run_continuous()
            
        except asyncio.CancelledError:
            logger.info(f"Agent {agent_name} was cancelled")
            raise
            
        except Exception as e:
            logger.error(f"Agent {agent_name} failed with error: {e}")
            agent.status = AgentStatus.ERROR
            
            # Attempt recovery after delay
            await asyncio.sleep(30)
            if self.is_running and agent.config.enabled:
                logger.info(f"Attempting to restart agent: {agent_name}")
                try:
                    agent.status = AgentStatus.ACTIVE
                    await agent.run_continuous()
                except Exception as recovery_error:
                    logger.error(f"Failed to recover agent {agent_name}: {recovery_error}")
    
    async def pause_agent(self, agent_name: str) -> bool:
        """Pause a specific agent"""
        if agent_name not in self.agents:
            logger.error(f"Agent {agent_name} not found")
            return False
        
        try:
            agent = self.agents[agent_name]
            await agent.pause()
            logger.info(f"Paused agent: {agent_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to pause agent {agent_name}: {e}")
            return False
    
    async def resume_agent(self, agent_name: str) -> bool:
        """Resume a specific agent"""
        if agent_name not in self.agents:
            logger.error(f"Agent {agent_name} not found")
            return False
        
        try:
            agent = self.agents[agent_name]
            await agent.resume()
            logger.info(f"Resumed agent: {agent_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to resume agent {agent_name}: {e}")
            return False
    
    def get_agent_status(self, agent_name: Optional[str] = None) -> Dict[str, Any]:
        """Get status of agents"""
        if agent_name:
            if agent_name not in self.agents:
                return {"error": f"Agent {agent_name} not found"}
            return self.agents[agent_name].get_status()
        
        # Return status of all agents
        status = {
            "manager_running": self.is_running,
            "total_agents": len(self.agents),
            "active_tasks": len(self.agent_tasks),
            "agents": {}
        }
        
        for name, agent in self.agents.items():
            agent_status = agent.get_status()
            agent_status["task_running"] = name in self.agent_tasks and not self.agent_tasks[name].done()
            status["agents"][name] = agent_status
        
        return status
    
    def get_agent(self, agent_name: str) -> Optional[BaseAgent]:
        """Get agent instance by name"""
        return self.agents.get(agent_name)
    
    async def send_message_to_agent(self, agent_name: str, message: str, sender: str = "system") -> Dict[str, Any]:
        """Send message to specific agent"""
        if agent_name not in self.agents:
            return {"error": f"Agent {agent_name} not found"}
        
        try:
            agent = self.agents[agent_name]
            
            # Add message to agent's conversation
            agent.add_to_conversation("user", f"Message from {sender}: {message}")
            
            # Process message with LLM
            messages = [{"role": "user", "content": message}]
            response = await agent.call_llm(messages)
            
            # Add response to conversation
            agent.add_to_conversation("assistant", response["content"])
            
            return {
                "agent": agent_name,
                "response": response["content"],
                "timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Failed to send message to agent {agent_name}: {e}")
            return {"error": str(e)}
    
    async def broadcast_message(self, message: str, sender: str = "system", exclude: List[str] = None) -> Dict[str, Any]:
        """Broadcast message to all agents"""
        if exclude is None:
            exclude = []
        
        responses = {}
        
        for agent_name in self.agents:
            if agent_name not in exclude:
                response = await self.send_message_to_agent(agent_name, message, sender)
                responses[agent_name] = response
        
        return {
            "broadcast_message": message,
            "sender": sender,
            "responses": responses,
            "timestamp": datetime.now().isoformat(),
        }
    
    async def get_agent_insights(self) -> Dict[str, Any]:
        """Get insights from all agents"""
        insights = {
            "timestamp": datetime.now().isoformat(),
            "agent_insights": {}
        }
        
        for agent_name, agent in self.agents.items():
            try:
                # Get recent conversation or analysis
                if hasattr(agent, 'last_market_analysis') and agent.last_market_analysis:
                    insights["agent_insights"][agent_name] = {
                        "type": "market_analysis",
                        "data": agent.last_market_analysis
                    }
                elif hasattr(agent, 'candidate_pool') and agent.candidate_pool:
                    insights["agent_insights"][agent_name] = {
                        "type": "stock_candidates",
                        "data": {"candidates": len(agent.candidate_pool)}
                    }
                elif hasattr(agent, 'last_risk_assessment') and agent.last_risk_assessment:
                    insights["agent_insights"][agent_name] = {
                        "type": "risk_assessment",
                        "data": agent.last_risk_assessment
                    }
                else:
                    insights["agent_insights"][agent_name] = {
                        "type": "status",
                        "data": agent.get_status()
                    }
                    
            except Exception as e:
                logger.error(f"Failed to get insights from agent {agent_name}: {e}")
                insights["agent_insights"][agent_name] = {"error": str(e)}
        
        return insights
    
    async def coordinate_agents(self) -> Dict[str, Any]:
        """Coordinate agents for collaborative decision making"""
        if not self.coordination_enabled:
            return {"message": "Agent coordination is disabled"}
        
        try:
            logger.info("Starting agent coordination")
            
            # Get insights from all agents
            insights = await self.get_agent_insights()
            
            # Create coordination message
            coordination_message = self._create_coordination_message(insights)
            
            # Send coordination message to relevant agents
            coordination_responses = await self.broadcast_message(
                coordination_message,
                sender="coordination_service"
            )
            
            return {
                "coordination_completed": True,
                "insights": insights,
                "coordination_message": coordination_message,
                "responses": coordination_responses,
                "timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Agent coordination failed: {e}")
            return {"error": str(e)}
    
    def _create_coordination_message(self, insights: Dict[str, Any]) -> str:
        """Create coordination message based on agent insights"""
        message_parts = ["Agent Coordination Update:"]
        
        agent_insights = insights.get("agent_insights", {})
        
        # Market analysis from Market Analyst
        if "market_analyst" in agent_insights:
            market_data = agent_insights["market_analyst"].get("data", {})
            if isinstance(market_data, dict) and "market_trend" in market_data:
                message_parts.append(f"Market Trend: {market_data['market_trend']}")
                message_parts.append(f"Confidence: {market_data.get('confidence_level', 'MEDIUM')}")
        
        # Stock candidates from Stock Selector
        if "stock_selector" in agent_insights:
            selector_data = agent_insights["stock_selector"].get("data", {})
            if isinstance(selector_data, dict) and "candidates" in selector_data:
                message_parts.append(f"Stock Candidates: {selector_data['candidates']} available")
        
        # Risk assessment from Risk Controller
        if "risk_controller" in agent_insights:
            risk_data = agent_insights["risk_controller"].get("data", {})
            if isinstance(risk_data, dict) and "portfolio_risk_level" in risk_data:
                message_parts.append(f"Portfolio Risk: {risk_data['portfolio_risk_level']}")
        
        message_parts.append("Please consider this information in your decision-making process.")
        
        return "\n".join(message_parts)
    
    async def shutdown(self) -> None:
        """Shutdown the agent manager"""
        try:
            logger.info("Shutting down Agent Manager")
            await self.stop_all_agents()
            self.agents.clear()
            logger.info("Agent Manager shutdown complete")
            
        except Exception as e:
            logger.error(f"Error during Agent Manager shutdown: {e}")
            raise
