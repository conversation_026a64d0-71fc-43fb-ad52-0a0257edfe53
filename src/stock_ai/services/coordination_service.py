"""
Coordination Service - Coordinates multi-agent interactions and decision making
"""

import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from enum import Enum

from loguru import logger

from .agent_manager import AgentManager
from ..tools import AlertTool, NotificationTool


class CoordinationMode(str, Enum):
    """Coordination modes"""
    AUTONOMOUS = "AUTONOMOUS"  # Agents work independently
    COLLABORATIVE = "COLLABORATIVE"  # Agents share information
    HIERARCHICAL = "HIERARCHICAL"  # Agents follow hierarchy


class CoordinationService:
    """Service for coordinating multi-agent interactions"""
    
    def __init__(self, agent_manager: AgentManager):
        self.agent_manager = agent_manager
        self.coordination_mode = CoordinationMode.COLLABORATIVE
        self.coordination_interval = 600  # 10 minutes
        self.last_coordination = None
        self.coordination_history = []
        self.is_running = False
        self.coordination_task = None
        
        # Tools for coordination
        self.alert_tool = AlertTool()
        self.notification_tool = NotificationTool()
    
    async def start(self) -> None:
        """Start coordination service"""
        if self.is_running:
            logger.warning("Coordination service is already running")
            return
        
        try:
            logger.info("Starting Coordination Service")
            self.is_running = True
            
            # Start coordination loop
            self.coordination_task = asyncio.create_task(
                self._coordination_loop(),
                name="coordination_service"
            )
            
            logger.info("Coordination Service started")
            
        except Exception as e:
            logger.error(f"Failed to start Coordination Service: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop coordination service"""
        if not self.is_running:
            logger.warning("Coordination service is not running")
            return
        
        try:
            logger.info("Stopping Coordination Service")
            self.is_running = False
            
            if self.coordination_task and not self.coordination_task.done():
                self.coordination_task.cancel()
                try:
                    await self.coordination_task
                except asyncio.CancelledError:
                    logger.info("Coordination task cancelled")
            
            logger.info("Coordination Service stopped")
            
        except Exception as e:
            logger.error(f"Error stopping Coordination Service: {e}")
    
    async def _coordination_loop(self) -> None:
        """Main coordination loop"""
        while self.is_running:
            try:
                await self._perform_coordination()
                await asyncio.sleep(self.coordination_interval)
                
            except asyncio.CancelledError:
                logger.info("Coordination loop cancelled")
                break
                
            except Exception as e:
                logger.error(f"Error in coordination loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying
    
    async def _perform_coordination(self) -> None:
        """Perform coordination between agents"""
        try:
            logger.info("Performing agent coordination")
            
            if self.coordination_mode == CoordinationMode.AUTONOMOUS:
                # Agents work independently, minimal coordination
                await self._autonomous_coordination()
                
            elif self.coordination_mode == CoordinationMode.COLLABORATIVE:
                # Agents share information and collaborate
                await self._collaborative_coordination()
                
            elif self.coordination_mode == CoordinationMode.HIERARCHICAL:
                # Agents follow hierarchy and chain of command
                await self._hierarchical_coordination()
            
            self.last_coordination = datetime.now()
            logger.info("Agent coordination completed")
            
        except Exception as e:
            logger.error(f"Coordination failed: {e}")
    
    async def _autonomous_coordination(self) -> None:
        """Minimal coordination for autonomous mode"""
        # Just check agent health and send status updates
        agent_status = self.agent_manager.get_agent_status()
        
        # Check for failed agents
        failed_agents = []
        for agent_name, status in agent_status.get("agents", {}).items():
            if status.get("status") == "ERROR":
                failed_agents.append(agent_name)
        
        if failed_agents:
            await self.alert_tool.safe_execute(
                alert_type="system_alert",
                level="ERROR",
                title=f"Agent Failures Detected",
                message=f"Failed agents: {', '.join(failed_agents)}",
                data={"failed_agents": failed_agents}
            )
    
    async def _collaborative_coordination(self) -> None:
        """Full collaboration between agents"""
        # Step 1: Gather insights from all agents
        insights = await self.agent_manager.get_agent_insights()
        
        # Step 2: Analyze coordination needs
        coordination_needs = self._analyze_coordination_needs(insights)
        
        # Step 3: Facilitate information sharing
        if coordination_needs.get("market_update_needed", False):
            await self._coordinate_market_update(insights)
        
        if coordination_needs.get("risk_review_needed", False):
            await self._coordinate_risk_review(insights)
        
        if coordination_needs.get("portfolio_rebalancing_needed", False):
            await self._coordinate_portfolio_rebalancing(insights)
        
        # Step 4: Record coordination
        self._record_coordination(insights, coordination_needs)
    
    async def _hierarchical_coordination(self) -> None:
        """Hierarchical coordination with chain of command"""
        # Hierarchy: Risk Controller -> Portfolio Manager -> Trading Decision Maker -> Stock Selector
        # Market Analyst provides input to all
        
        # Step 1: Get market analysis
        market_analyst = self.agent_manager.get_agent("market_analyst")
        if market_analyst and hasattr(market_analyst, 'last_market_analysis'):
            market_analysis = market_analyst.last_market_analysis
            
            # Share with all agents
            await self.agent_manager.broadcast_message(
                f"Market Update: {market_analysis.get('market_trend', 'NEUTRAL')} trend with {market_analysis.get('confidence_level', 'MEDIUM')} confidence",
                sender="market_analyst"
            )
        
        # Step 2: Risk Controller reviews and sets constraints
        risk_controller = self.agent_manager.get_agent("risk_controller")
        if risk_controller and hasattr(risk_controller, 'last_risk_assessment'):
            risk_assessment = risk_controller.last_risk_assessment
            
            if risk_assessment and risk_assessment.get("portfolio_risk_level") in ["ORANGE", "RED"]:
                # Send risk constraints to other agents
                await self.agent_manager.send_message_to_agent(
                    "portfolio_manager",
                    f"Risk Alert: Portfolio risk is {risk_assessment['portfolio_risk_level']}. Consider defensive measures.",
                    sender="risk_controller"
                )
                
                await self.agent_manager.send_message_to_agent(
                    "trading_decision_maker",
                    f"Risk Constraint: Reduce position sizes due to {risk_assessment['portfolio_risk_level']} risk level.",
                    sender="risk_controller"
                )
    
    def _analyze_coordination_needs(self, insights: Dict[str, Any]) -> Dict[str, bool]:
        """Analyze what coordination is needed"""
        needs = {
            "market_update_needed": False,
            "risk_review_needed": False,
            "portfolio_rebalancing_needed": False,
            "trading_halt_needed": False,
        }
        
        agent_insights = insights.get("agent_insights", {})
        
        # Check if market analysis has significant changes
        if "market_analyst" in agent_insights:
            market_data = agent_insights["market_analyst"].get("data", {})
            if isinstance(market_data, dict):
                market_trend = market_data.get("market_trend")
                confidence = market_data.get("confidence_level")
                
                if market_trend in ["BULLISH", "BEARISH"] and confidence == "HIGH":
                    needs["market_update_needed"] = True
        
        # Check if risk levels are elevated
        if "risk_controller" in agent_insights:
            risk_data = agent_insights["risk_controller"].get("data", {})
            if isinstance(risk_data, dict):
                risk_level = risk_data.get("portfolio_risk_level")
                
                if risk_level in ["ORANGE", "RED"]:
                    needs["risk_review_needed"] = True
                    
                if risk_level == "RED":
                    needs["trading_halt_needed"] = True
        
        return needs
    
    async def _coordinate_market_update(self, insights: Dict[str, Any]) -> None:
        """Coordinate market update across agents"""
        market_data = insights.get("agent_insights", {}).get("market_analyst", {}).get("data", {})
        
        if not isinstance(market_data, dict):
            return
        
        update_message = f"""Market Update Coordination:
- Trend: {market_data.get('market_trend', 'NEUTRAL')}
- Confidence: {market_data.get('confidence_level', 'MEDIUM')}
- Key Risks: {', '.join(market_data.get('key_risks', []))}
- Opportunities: {', '.join(market_data.get('opportunities', []))}

Please adjust your strategies accordingly."""
        
        # Send to relevant agents
        await self.agent_manager.send_message_to_agent(
            "stock_selector",
            update_message,
            sender="coordination_service"
        )
        
        await self.agent_manager.send_message_to_agent(
            "trading_decision_maker",
            update_message,
            sender="coordination_service"
        )
        
        await self.agent_manager.send_message_to_agent(
            "portfolio_manager",
            update_message,
            sender="coordination_service"
        )
    
    async def _coordinate_risk_review(self, insights: Dict[str, Any]) -> None:
        """Coordinate risk review across agents"""
        risk_data = insights.get("agent_insights", {}).get("risk_controller", {}).get("data", {})
        
        if not isinstance(risk_data, dict):
            return
        
        risk_message = f"""Risk Review Coordination:
- Portfolio Risk Level: {risk_data.get('portfolio_risk_level', 'UNKNOWN')}
- VaR: {risk_data.get('var_metrics', {}).get('var_percentage', 0):.2f}%
- High Risk Positions: {', '.join(risk_data.get('high_risk_positions', []))}
- Recommendations: {'; '.join(risk_data.get('recommendations', []))}

Immediate risk mitigation required."""
        
        # Send to all trading agents
        await self.agent_manager.broadcast_message(
            risk_message,
            sender="risk_controller",
            exclude=["risk_controller"]
        )
        
        # Generate alert
        await self.alert_tool.safe_execute(
            alert_type="risk_alert",
            level="WARNING",
            title="Coordinated Risk Review",
            message=f"Portfolio risk level: {risk_data.get('portfolio_risk_level', 'UNKNOWN')}",
            data=risk_data
        )
    
    async def _coordinate_portfolio_rebalancing(self, insights: Dict[str, Any]) -> None:
        """Coordinate portfolio rebalancing"""
        # This would involve complex coordination between Portfolio Manager and Trading Decision Maker
        rebalancing_message = "Portfolio rebalancing coordination initiated. Review allocation targets and execute rebalancing trades."
        
        await self.agent_manager.send_message_to_agent(
            "portfolio_manager",
            rebalancing_message,
            sender="coordination_service"
        )
        
        await self.agent_manager.send_message_to_agent(
            "trading_decision_maker",
            rebalancing_message,
            sender="coordination_service"
        )
    
    def _record_coordination(self, insights: Dict[str, Any], coordination_needs: Dict[str, bool]) -> None:
        """Record coordination event"""
        coordination_record = {
            "timestamp": datetime.now().isoformat(),
            "mode": self.coordination_mode.value,
            "insights": insights,
            "coordination_needs": coordination_needs,
            "actions_taken": [need for need, required in coordination_needs.items() if required],
        }
        
        self.coordination_history.append(coordination_record)
        
        # Keep history manageable
        if len(self.coordination_history) > 100:
            self.coordination_history = self.coordination_history[-50:]
    
    def set_coordination_mode(self, mode: CoordinationMode) -> None:
        """Set coordination mode"""
        self.coordination_mode = mode
        logger.info(f"Coordination mode set to: {mode.value}")
    
    def set_coordination_interval(self, interval_seconds: int) -> None:
        """Set coordination interval"""
        self.coordination_interval = max(60, interval_seconds)  # Minimum 1 minute
        logger.info(f"Coordination interval set to: {self.coordination_interval} seconds")
    
    def get_coordination_status(self) -> Dict[str, Any]:
        """Get coordination service status"""
        return {
            "is_running": self.is_running,
            "coordination_mode": self.coordination_mode.value,
            "coordination_interval": self.coordination_interval,
            "last_coordination": self.last_coordination.isoformat() if self.last_coordination else None,
            "coordination_history_length": len(self.coordination_history),
            "next_coordination": (
                self.last_coordination + timedelta(seconds=self.coordination_interval)
            ).isoformat() if self.last_coordination else None,
        }
    
    def get_coordination_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent coordination history"""
        return self.coordination_history[-limit:] if self.coordination_history else []
