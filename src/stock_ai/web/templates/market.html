{% extends "base.html" %}

{% block title %}市场分析 - Stock AI Agents{% endblock %}

{% block page_title %}市场分析{% endblock %}
{% block page_description %}实时市场数据和智能分析{% endblock %}

{% block content %}
<!-- Market Overview -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">上证指数</h6>
                        <h4>3,245.67</h4>
                        <small>+1.25% (+40.12)</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-up fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">深证成指</h6>
                        <h4>11,856.23</h4>
                        <small>+0.85% (+99.87)</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-up fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">创业板指</h6>
                        <h4>2,456.89</h4>
                        <small>-0.45% (-11.23)</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-down fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">市场情绪</h6>
                        <h4>乐观</h4>
                        <small>情绪指数: 72</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-emoji-smile fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Market Analysis Tabs -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-pills card-header-pills" id="market-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="watchlist-tab" data-bs-toggle="pill" data-bs-target="#watchlist" type="button" role="tab">
                            <i class="bi bi-star me-1"></i>自选股
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="news-tab" data-bs-toggle="pill" data-bs-target="#news" type="button" role="tab">
                            <i class="bi bi-newspaper me-1"></i>市场资讯
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="sentiment-tab" data-bs-toggle="pill" data-bs-target="#sentiment" type="button" role="tab">
                            <i class="bi bi-heart-pulse me-1"></i>情绪分析
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="economic-tab" data-bs-toggle="pill" data-bs-target="#economic" type="button" role="tab">
                            <i class="bi bi-graph-up-arrow me-1"></i>经济指标
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="market-tab-content">
                    <!-- Watchlist Tab -->
                    <div class="tab-pane fade show active" id="watchlist" role="tabpanel">
                        <div id="watchlist-content">
                            <div class="text-center">
                                <div class="loading-spinner"></div>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- News Tab -->
                    <div class="tab-pane fade" id="news" role="tabpanel">
                        <div id="news-content">
                            <div class="text-center">
                                <div class="loading-spinner"></div>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sentiment Tab -->
                    <div class="tab-pane fade" id="sentiment" role="tabpanel">
                        <div id="sentiment-content">
                            <div class="text-center">
                                <div class="loading-spinner"></div>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Economic Tab -->
                    <div class="tab-pane fade" id="economic" role="tabpanel">
                        <div id="economic-content">
                            <div class="text-center">
                                <div class="loading-spinner"></div>
                                <p class="mt-2">加载中...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Market Heatmap -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-grid-3x3-gap me-2"></i>行业热力图</h5>
            </div>
            <div class="card-body">
                <div id="sector-heatmap">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <div class="card bg-success text-white text-center">
                                <div class="card-body py-3">
                                    <h6 class="card-title mb-1">科技</h6>
                                    <h5 class="mb-0">+2.45%</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-success text-white text-center">
                                <div class="card-body py-3">
                                    <h6 class="card-title mb-1">消费</h6>
                                    <h5 class="mb-0">+1.85%</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-warning text-white text-center">
                                <div class="card-body py-3">
                                    <h6 class="card-title mb-1">金融</h6>
                                    <h5 class="mb-0">+0.25%</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-danger text-white text-center">
                                <div class="card-body py-3">
                                    <h6 class="card-title mb-1">地产</h6>
                                    <h5 class="mb-0">-1.25%</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-success text-white text-center">
                                <div class="card-body py-3">
                                    <h6 class="card-title mb-1">医药</h6>
                                    <h5 class="mb-0">+1.65%</h5>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-success text-white text-center">
                                <div class="card-body py-3">
                                    <h6 class="card-title mb-1">新能源</h6>
                                    <h5 class="mb-0">+3.15%</h5>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadWatchlist();
    setupTabHandlers();
});

function setupTabHandlers() {
    const tabs = document.querySelectorAll('#market-tabs button[data-bs-toggle="pill"]');
    tabs.forEach(tab => {
        tab.addEventListener('shown.bs.tab', function(event) {
            const targetId = event.target.getAttribute('data-bs-target').substring(1);
            loadTabContent(targetId);
        });
    });
}

async function loadTabContent(tabId) {
    switch (tabId) {
        case 'watchlist':
            await loadWatchlist();
            break;
        case 'news':
            await loadNews();
            break;
        case 'sentiment':
            await loadSentiment();
            break;
        case 'economic':
            await loadEconomic();
            break;
    }
}

async function loadWatchlist() {
    try {
        const response = await fetch('/api/market/watchlist');
        const data = await response.json();
        updateWatchlist(data);
    } catch (error) {
        console.error('Error loading watchlist:', error);
        document.getElementById('watchlist-content').innerHTML = 
            '<p class="text-danger">加载自选股失败</p>';
    }
}

function updateWatchlist(data) {
    const container = document.getElementById('watchlist-content');
    
    if (!data.watchlist || data.watchlist.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无自选股数据</p>';
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>股票</th>
                        <th>现价</th>
                        <th>涨跌</th>
                        <th>涨跌幅</th>
                        <th>成交量</th>
                        <th>市值</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;

    data.watchlist.forEach(stock => {
        const changeClass = stock.change >= 0 ? 'text-success' : 'text-danger';
        const changeIcon = stock.change >= 0 ? 'bi-arrow-up' : 'bi-arrow-down';

        html += `
            <tr>
                <td>
                    <div>
                        <div class="fw-bold">${stock.symbol}</div>
                        <small class="text-muted">${stock.name}</small>
                    </div>
                </td>
                <td class="fw-bold">¥${stock.price.toFixed(2)}</td>
                <td class="${changeClass}">
                    <i class="bi ${changeIcon}"></i>
                    ${stock.change >= 0 ? '+' : ''}${stock.change.toFixed(2)}
                </td>
                <td class="${changeClass}">
                    ${stock.change_percent >= 0 ? '+' : ''}${stock.change_percent.toFixed(2)}%
                </td>
                <td>${formatNumber(stock.volume)}</td>
                <td>${stock.market_cap ? formatNumber(stock.market_cap) : 'N/A'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewStockDetail('${stock.symbol}')">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

async function loadNews() {
    try {
        const response = await fetch('/api/market/news?limit=10');
        const data = await response.json();
        updateNews(data);
    } catch (error) {
        console.error('Error loading news:', error);
        document.getElementById('news-content').innerHTML = 
            '<p class="text-danger">加载市场资讯失败</p>';
    }
}

function updateNews(data) {
    const container = document.getElementById('news-content');
    
    if (!data.articles || data.articles.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无市场资讯</p>';
        return;
    }

    let html = '<div class="row">';
    
    data.articles.forEach(article => {
        const sentimentClass = article.sentiment === 'POSITIVE' ? 'success' :
                              article.sentiment === 'NEGATIVE' ? 'danger' : 'secondary';
        
        html += `
            <div class="col-md-6 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <h6 class="card-title">${article.title}</h6>
                            <span class="badge bg-${sentimentClass}">${article.sentiment}</span>
                        </div>
                        <p class="card-text">${article.summary}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">${article.source}</small>
                            <small class="text-muted">${new Date(article.published_at).toLocaleString()}</small>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

async function loadSentiment() {
    try {
        const response = await fetch('/api/market/sentiment');
        const data = await response.json();
        updateSentiment(data);
    } catch (error) {
        console.error('Error loading sentiment:', error);
        document.getElementById('sentiment-content').innerHTML = 
            '<p class="text-danger">加载情绪分析失败</p>';
    }
}

function updateSentiment(data) {
    const container = document.getElementById('sentiment-content');
    
    const sentimentClass = data.overall_sentiment === 'BULLISH' ? 'success' :
                          data.overall_sentiment === 'BEARISH' ? 'danger' : 'warning';
    
    const sentimentText = data.overall_sentiment === 'BULLISH' ? '看涨' :
                         data.overall_sentiment === 'BEARISH' ? '看跌' : '中性';

    let html = `
        <div class="row">
            <div class="col-md-4">
                <div class="card bg-${sentimentClass} text-white text-center">
                    <div class="card-body">
                        <h2>${sentimentText}</h2>
                        <p class="mb-0">整体市场情绪</p>
                        <small>情绪得分: ${data.sentiment_score}</small>
                    </div>
                </div>
            </div>
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <h6>情绪分布</h6>
                        <div class="row">
                            <div class="col-4 text-center">
                                <h4 class="text-success">${data.sentiment_distribution.POSITIVE}</h4>
                                <p class="text-muted mb-0">积极</p>
                            </div>
                            <div class="col-4 text-center">
                                <h4 class="text-secondary">${data.sentiment_distribution.NEUTRAL}</h4>
                                <p class="text-muted mb-0">中性</p>
                            </div>
                            <div class="col-4 text-center">
                                <h4 class="text-danger">${data.sentiment_distribution.NEGATIVE}</h4>
                                <p class="text-muted mb-0">消极</p>
                            </div>
                        </div>
                        <hr>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">分析文章数: ${data.total_articles}</small>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">置信度: ${data.confidence}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

async function loadEconomic() {
    try {
        const response = await fetch('/api/market/economic?indicator=gdp_data');
        const data = await response.json();
        updateEconomic(data);
    } catch (error) {
        console.error('Error loading economic data:', error);
        document.getElementById('economic-content').innerHTML = 
            '<p class="text-danger">加载经济指标失败</p>';
    }
}

function updateEconomic(data) {
    const container = document.getElementById('economic-content');
    
    let html = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">GDP数据 - ${data.country}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <h4 class="text-primary">${data.current_gdp.value}万亿</h4>
                                <p class="text-muted mb-0">当前GDP</p>
                            </div>
                            <div class="col-6">
                                <h4 class="text-success">+${data.current_gdp.growth_rate}%</h4>
                                <p class="text-muted mb-0">增长率</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">GDP构成</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>消费</span>
                                <span>${data.components.consumption}%</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar" style="width: ${data.components.consumption}%"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>投资</span>
                                <span>${data.components.investment}%</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-success" style="width: ${data.components.investment}%"></div>
                            </div>
                        </div>
                        <div class="mb-2">
                            <div class="d-flex justify-content-between">
                                <span>政府支出</span>
                                <span>${data.components.government}%</span>
                            </div>
                            <div class="progress" style="height: 6px;">
                                <div class="progress-bar bg-warning" style="width: ${data.components.government}%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = html;
}

function viewStockDetail(symbol) {
    showAlert('info', `查看 ${symbol} 详情功能正在开发中`);
}

// Override the global refresh function
async function refreshData() {
    const activeTab = document.querySelector('#market-tabs .nav-link.active');
    if (activeTab) {
        const targetId = activeTab.getAttribute('data-bs-target').substring(1);
        await loadTabContent(targetId);
    }
}
</script>
{% endblock %}
