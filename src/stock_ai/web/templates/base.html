<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Stock AI Agents Dashboard{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #06b6d4;
            --dark-color: #1e293b;
            --light-color: #f8fafc;
        }

        body {
            background-color: var(--light-color);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            background: linear-gradient(135deg, var(--dark-color) 0%, #334155 100%);
            min-height: 100vh;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar .nav-link {
            color: #cbd5e1;
            padding: 12px 20px;
            margin: 4px 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(5px);
        }

        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }

        .main-content {
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            margin: 20px;
            padding: 30px;
        }

        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
            color: white;
            border-radius: 12px 12px 0 0 !important;
            border: none;
            padding: 15px 20px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85em;
            font-weight: 600;
        }

        .status-running { background-color: var(--success-color); color: white; }
        .status-stopped { background-color: var(--danger-color); color: white; }
        .status-paused { background-color: var(--warning-color); color: white; }
        .status-active { background-color: var(--success-color); color: white; }
        .status-inactive { background-color: var(--secondary-color); color: white; }

        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, #3b82f6 100%);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }

        .table {
            border-radius: 8px;
            overflow: hidden;
        }

        .table thead th {
            background-color: var(--light-color);
            border: none;
            font-weight: 600;
            color: var(--dark-color);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .alert {
            border: none;
            border-radius: 8px;
        }

        .progress {
            height: 8px;
            border-radius: 4px;
        }

        .nav-pills .nav-link {
            border-radius: 8px;
            margin-right: 8px;
        }

        .nav-pills .nav-link.active {
            background-color: var(--primary-color);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 p-0">
                <div class="sidebar">
                    <div class="p-4">
                        <h4 class="text-white mb-4">
                            <i class="bi bi-robot"></i> Stock AI
                        </h4>
                    </div>
                    <nav class="nav flex-column">
                        <a class="nav-link {% if request.url.path == '/' %}active{% endif %}" href="/">
                            <i class="bi bi-speedometer2 me-2"></i> 仪表盘
                        </a>
                        <a class="nav-link {% if request.url.path == '/agents' %}active{% endif %}" href="/agents">
                            <i class="bi bi-cpu me-2"></i> 智能体
                        </a>
                        <a class="nav-link {% if request.url.path == '/portfolio' %}active{% endif %}" href="/portfolio">
                            <i class="bi bi-briefcase me-2"></i> 投资组合
                        </a>
                        <a class="nav-link {% if request.url.path == '/market' %}active{% endif %}" href="/market">
                            <i class="bi bi-graph-up me-2"></i> 市场分析
                        </a>
                        <a class="nav-link {% if request.url.path == '/settings' %}active{% endif %}" href="/settings">
                            <i class="bi bi-gear me-2"></i> 设置
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-10">
                <div class="main-content">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <div>
                            <h2 class="mb-1">{% block page_title %}仪表盘{% endblock %}</h2>
                            <p class="text-muted mb-0">{% block page_description %}多智能体股票交易系统{% endblock %}</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary me-2" onclick="refreshData()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                            <div class="dropdown d-inline">
                                <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-gear"></i> 操作
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#" onclick="startSystem()">
                                        <i class="bi bi-play-fill text-success"></i> 启动系统
                                    </a></li>
                                    <li><a class="dropdown-item" href="#" onclick="stopSystem()">
                                        <i class="bi bi-stop-fill text-danger"></i> 停止系统
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="/api/docs" target="_blank">
                                        <i class="bi bi-book"></i> API 文档
                                    </a></li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Content -->
                    {% block content %}{% endblock %}
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Common JavaScript -->
    <script>
        // Global functions
        async function refreshData() {
            location.reload();
        }

        async function startSystem() {
            try {
                const response = await fetch('/api/system/start', { method: 'POST' });
                const data = await response.json();
                showAlert('success', '系统启动请求已发送');
                setTimeout(refreshData, 2000);
            } catch (error) {
                showAlert('danger', '启动系统失败: ' + error.message);
            }
        }

        async function stopSystem() {
            if (confirm('确定要停止系统吗？')) {
                try {
                    const response = await fetch('/api/system/stop', { method: 'POST' });
                    const data = await response.json();
                    showAlert('warning', '系统停止请求已发送');
                    setTimeout(refreshData, 2000);
                } catch (error) {
                    showAlert('danger', '停止系统失败: ' + error.message);
                }
            }
        }

        function showAlert(type, message) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 5000);
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('zh-CN', {
                style: 'currency',
                currency: 'CNY'
            }).format(amount);
        }

        function formatPercent(value) {
            return (value >= 0 ? '+' : '') + value.toFixed(2) + '%';
        }

        function formatNumber(num) {
            if (num >= 1e8) return (num / 1e8).toFixed(1) + '亿';
            if (num >= 1e4) return (num / 1e4).toFixed(1) + '万';
            return num.toLocaleString();
        }

        // Auto refresh every 30 seconds
        setInterval(() => {
            if (document.visibilityState === 'visible') {
                refreshData();
            }
        }, 30000);
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
