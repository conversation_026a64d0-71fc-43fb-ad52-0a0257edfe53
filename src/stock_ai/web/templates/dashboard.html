{% extends "base.html" %}

{% block title %}仪表盘 - Stock AI Agents{% endblock %}

{% block content %}
<!-- System Status Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">系统状态</h6>
                        <h3 id="system-status">加载中...</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-power fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">活跃智能体</h6>
                        <h3 id="active-agents">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cpu fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">投资组合价值</h6>
                        <h3 id="portfolio-value">¥0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-briefcase fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">今日盈亏</h6>
                        <h3 id="daily-pnl">¥0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-up fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>投资组合表现</h5>
            </div>
            <div class="card-body">
                <canvas id="portfolioChart" height="100"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>资产配置</h5>
            </div>
            <div class="card-body">
                <canvas id="allocationChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Agents Status and Market News -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-cpu me-2"></i>智能体状态</h5>
            </div>
            <div class="card-body">
                <div id="agents-status">
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-newspaper me-2"></i>市场资讯</h5>
            </div>
            <div class="card-body">
                <div id="market-news">
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-clock-history me-2"></i>最近活动</h5>
            </div>
            <div class="card-body">
                <div id="recent-logs">
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let portfolioChart, allocationChart;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    loadDashboardData();
    initCharts();
});

async function loadDashboardData() {
    try {
        // Load system status
        const statusResponse = await fetch('/api/status');
        const statusData = await statusResponse.json();
        updateSystemStatus(statusData);

        // Load portfolio data
        const portfolioResponse = await fetch('/api/portfolio');
        const portfolioData = await portfolioResponse.json();
        updatePortfolioData(portfolioData);

        // Load agents status
        const agentsResponse = await fetch('/api/agents');
        const agentsData = await agentsResponse.json();
        updateAgentsStatus(agentsData);

        // Load market news
        const newsResponse = await fetch('/api/market/news?limit=5');
        const newsData = await newsResponse.json();
        updateMarketNews(newsData);

        // Load recent logs
        const logsResponse = await fetch('/api/logs?limit=10');
        const logsData = await logsResponse.json();
        updateRecentLogs(logsData);

    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showAlert('danger', '加载数据失败: ' + error.message);
    }
}

function updateSystemStatus(data) {
    document.getElementById('system-status').textContent = data.status === 'RUNNING' ? '运行中' : '已停止';
    document.getElementById('active-agents').textContent = `${data.active_agents}/${data.agents_count}`;
}

function updatePortfolioData(data) {
    document.getElementById('portfolio-value').textContent = formatCurrency(data.total_value);
    document.getElementById('daily-pnl').textContent = formatCurrency(data.daily_pnl);
    
    // Update portfolio chart
    updatePortfolioChart(data);
}

function updateAgentsStatus(agents) {
    const container = document.getElementById('agents-status');
    
    if (agents.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无智能体数据</p>';
        return;
    }

    let html = '<div class="list-group list-group-flush">';
    agents.forEach(agent => {
        const statusClass = agent.status === 'ACTIVE' ? 'success' : 
                           agent.status === 'PAUSED' ? 'warning' : 'secondary';
        const statusText = agent.status === 'ACTIVE' ? '运行中' :
                          agent.status === 'PAUSED' ? '暂停' : '停止';
        
        html += `
            <div class="list-group-item d-flex justify-content-between align-items-center">
                <div>
                    <h6 class="mb-1">${agent.name.replace('_', ' ')}</h6>
                    <small class="text-muted">执行次数: ${agent.execution_count}</small>
                </div>
                <span class="badge bg-${statusClass}">${statusText}</span>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function updateMarketNews(data) {
    const container = document.getElementById('market-news');
    
    if (!data.articles || data.articles.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无市场资讯</p>';
        return;
    }

    let html = '<div class="list-group list-group-flush">';
    data.articles.slice(0, 5).forEach(article => {
        const sentimentClass = article.sentiment === 'POSITIVE' ? 'success' :
                              article.sentiment === 'NEGATIVE' ? 'danger' : 'secondary';
        
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${article.title}</h6>
                    <small class="badge bg-${sentimentClass}">${article.sentiment}</small>
                </div>
                <p class="mb-1">${article.summary}</p>
                <small class="text-muted">${article.source} - ${new Date(article.published_at).toLocaleString()}</small>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function updateRecentLogs(data) {
    const container = document.getElementById('recent-logs');
    
    if (!data.logs || data.logs.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无日志记录</p>';
        return;
    }

    let html = '<div class="list-group list-group-flush">';
    data.logs.slice(0, 10).forEach(log => {
        const levelClass = log.level === 'ERROR' ? 'danger' :
                          log.level === 'WARNING' ? 'warning' :
                          log.level === 'INFO' ? 'info' : 'secondary';
        
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">
                        <span class="badge bg-${levelClass} me-2">${log.level}</span>
                        ${log.agent}
                    </h6>
                    <small>${new Date(log.timestamp).toLocaleString()}</small>
                </div>
                <p class="mb-1">${log.message}</p>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function initCharts() {
    // Portfolio performance chart
    const portfolioCtx = document.getElementById('portfolioChart').getContext('2d');
    portfolioChart = new Chart(portfolioCtx, {
        type: 'line',
        data: {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月'],
            datasets: [{
                label: '投资组合价值',
                data: [1000000, 1020000, 1050000, 1030000, 1080000, 1050000],
                borderColor: 'rgb(37, 99, 235)',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: false,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value);
                        }
                    }
                }
            }
        }
    });

    // Asset allocation chart
    const allocationCtx = document.getElementById('allocationChart').getContext('2d');
    allocationChart = new Chart(allocationCtx, {
        type: 'doughnut',
        data: {
            labels: ['股票', '现金', '债券', '其他'],
            datasets: [{
                data: [70, 20, 8, 2],
                backgroundColor: [
                    'rgb(37, 99, 235)',
                    'rgb(16, 185, 129)',
                    'rgb(245, 158, 11)',
                    'rgb(107, 114, 128)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function updatePortfolioChart(data) {
    // This would update the chart with real data
    // For now, we'll keep the mock data
}

// Override the global refresh function for dashboard
async function refreshData() {
    await loadDashboardData();
}
</script>
{% endblock %}
