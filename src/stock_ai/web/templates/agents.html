{% extends "base.html" %}

{% block title %}智能体管理 - Stock AI Agents{% endblock %}

{% block page_title %}智能体管理{% endblock %}
{% block page_description %}管理和监控AI智能体的运行状态{% endblock %}

{% block content %}
<!-- Agents Overview Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">总智能体数</h6>
                        <h3 id="total-agents">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cpu fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">运行中</h6>
                        <h3 id="active-agents">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-play-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">暂停中</h6>
                        <h3 id="paused-agents">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-pause-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">已停止</h6>
                        <h3 id="stopped-agents">0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-stop-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Agents Control Panel -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-gear me-2"></i>批量操作</h5>
                    <div>
                        <button class="btn btn-success btn-sm me-2" onclick="startAllAgents()">
                            <i class="bi bi-play-fill"></i> 启动全部
                        </button>
                        <button class="btn btn-warning btn-sm me-2" onclick="pauseAllAgents()">
                            <i class="bi bi-pause-fill"></i> 暂停全部
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="stopAllAgents()">
                            <i class="bi bi-stop-fill"></i> 停止全部
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Agents List -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-list me-2"></i>智能体列表</h5>
            </div>
            <div class="card-body">
                <div id="agents-table">
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Agent Details Modal -->
<div class="modal fade" id="agentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">智能体详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="agent-details">
                <!-- Agent details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let agentsData = [];

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadAgentsData();
});

async function loadAgentsData() {
    try {
        const response = await fetch('/api/agents');
        agentsData = await response.json();
        updateAgentsOverview();
        updateAgentsTable();
    } catch (error) {
        console.error('Error loading agents data:', error);
        showAlert('danger', '加载智能体数据失败: ' + error.message);
    }
}

function updateAgentsOverview() {
    const total = agentsData.length;
    const active = agentsData.filter(a => a.status === 'ACTIVE').length;
    const paused = agentsData.filter(a => a.status === 'PAUSED').length;
    const stopped = agentsData.filter(a => a.status === 'INACTIVE').length;

    document.getElementById('total-agents').textContent = total;
    document.getElementById('active-agents').textContent = active;
    document.getElementById('paused-agents').textContent = paused;
    document.getElementById('stopped-agents').textContent = stopped;
}

function updateAgentsTable() {
    const container = document.getElementById('agents-table');
    
    if (agentsData.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">暂无智能体数据</p>';
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>名称</th>
                        <th>类型</th>
                        <th>状态</th>
                        <th>执行次数</th>
                        <th>错误次数</th>
                        <th>最后执行</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
    `;

    agentsData.forEach(agent => {
        const statusClass = getStatusClass(agent.status);
        const statusText = getStatusText(agent.status);
        const lastExecution = agent.last_execution ? 
            new Date(agent.last_execution).toLocaleString() : '从未执行';

        html += `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-cpu me-2 text-primary"></i>
                        <div>
                            <div class="fw-bold">${agent.name.replace('_', ' ')}</div>
                            <small class="text-muted">${agent.description || '无描述'}</small>
                        </div>
                    </div>
                </td>
                <td><span class="badge bg-info">${agent.type}</span></td>
                <td><span class="badge ${statusClass}">${statusText}</span></td>
                <td>${agent.execution_count}</td>
                <td>
                    ${agent.error_count > 0 ? 
                        `<span class="text-danger">${agent.error_count}</span>` : 
                        agent.error_count}
                </td>
                <td><small>${lastExecution}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        ${agent.status !== 'ACTIVE' ? 
                            `<button class="btn btn-outline-success" onclick="startAgent('${agent.name}')" title="启动">
                                <i class="bi bi-play-fill"></i>
                            </button>` : 
                            `<button class="btn btn-outline-danger" onclick="stopAgent('${agent.name}')" title="停止">
                                <i class="bi bi-stop-fill"></i>
                            </button>`}
                        <button class="btn btn-outline-primary" onclick="showAgentDetails('${agent.name}')" title="详情">
                            <i class="bi bi-info-circle"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

function getStatusClass(status) {
    switch (status) {
        case 'ACTIVE': return 'bg-success';
        case 'PAUSED': return 'bg-warning';
        case 'INACTIVE': return 'bg-secondary';
        case 'ERROR': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function getStatusText(status) {
    switch (status) {
        case 'ACTIVE': return '运行中';
        case 'PAUSED': return '暂停';
        case 'INACTIVE': return '已停止';
        case 'ERROR': return '错误';
        default: return '未知';
    }
}

async function startAgent(agentName) {
    try {
        const response = await fetch(`/api/agents/${agentName}/start`, { method: 'POST' });
        const data = await response.json();
        showAlert('success', `智能体 ${agentName} 启动请求已发送`);
        setTimeout(loadAgentsData, 2000);
    } catch (error) {
        showAlert('danger', `启动智能体失败: ${error.message}`);
    }
}

async function stopAgent(agentName) {
    try {
        const response = await fetch(`/api/agents/${agentName}/stop`, { method: 'POST' });
        const data = await response.json();
        showAlert('warning', `智能体 ${agentName} 停止请求已发送`);
        setTimeout(loadAgentsData, 2000);
    } catch (error) {
        showAlert('danger', `停止智能体失败: ${error.message}`);
    }
}

async function startAllAgents() {
    if (confirm('确定要启动所有智能体吗？')) {
        const inactiveAgents = agentsData.filter(a => a.status !== 'ACTIVE');
        for (const agent of inactiveAgents) {
            await startAgent(agent.name);
        }
    }
}

async function pauseAllAgents() {
    if (confirm('确定要暂停所有智能体吗？')) {
        showAlert('info', '暂停功能正在开发中');
    }
}

async function stopAllAgents() {
    if (confirm('确定要停止所有智能体吗？')) {
        const activeAgents = agentsData.filter(a => a.status === 'ACTIVE');
        for (const agent of activeAgents) {
            await stopAgent(agent.name);
        }
    }
}

function showAgentDetails(agentName) {
    const agent = agentsData.find(a => a.name === agentName);
    if (!agent) return;

    const modalBody = document.getElementById('agent-details');
    modalBody.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>名称</td><td>${agent.name}</td></tr>
                    <tr><td>类型</td><td>${agent.type}</td></tr>
                    <tr><td>状态</td><td><span class="badge ${getStatusClass(agent.status)}">${getStatusText(agent.status)}</span></td></tr>
                    <tr><td>描述</td><td>${agent.description || '无描述'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>运行统计</h6>
                <table class="table table-sm">
                    <tr><td>执行次数</td><td>${agent.execution_count}</td></tr>
                    <tr><td>错误次数</td><td>${agent.error_count}</td></tr>
                    <tr><td>最后执行</td><td>${agent.last_execution ? new Date(agent.last_execution).toLocaleString() : '从未执行'}</td></tr>
                    <tr><td>成功率</td><td>${agent.execution_count > 0 ? ((agent.execution_count - agent.error_count) / agent.execution_count * 100).toFixed(1) + '%' : 'N/A'}</td></tr>
                </table>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <h6>操作</h6>
                <div class="btn-group">
                    ${agent.status !== 'ACTIVE' ? 
                        `<button class="btn btn-success" onclick="startAgent('${agent.name}'); bootstrap.Modal.getInstance(document.getElementById('agentModal')).hide();">
                            <i class="bi bi-play-fill"></i> 启动
                        </button>` : 
                        `<button class="btn btn-danger" onclick="stopAgent('${agent.name}'); bootstrap.Modal.getInstance(document.getElementById('agentModal')).hide();">
                            <i class="bi bi-stop-fill"></i> 停止
                        </button>`}
                    <button class="btn btn-info" onclick="viewAgentLogs('${agent.name}')">
                        <i class="bi bi-file-text"></i> 查看日志
                    </button>
                </div>
            </div>
        </div>
    `;

    const modal = new bootstrap.Modal(document.getElementById('agentModal'));
    modal.show();
}

function viewAgentLogs(agentName) {
    showAlert('info', '日志查看功能正在开发中');
}

// Override the global refresh function
async function refreshData() {
    await loadAgentsData();
}
</script>
{% endblock %}
