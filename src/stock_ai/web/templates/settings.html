{% extends "base.html" %}

{% block title %}系统设置 - Stock AI Agents{% endblock %}

{% block page_title %}系统设置{% endblock %}
{% block page_description %}配置系统参数和智能体设置{% endblock %}

{% block content %}
<!-- Settings Navigation -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <ul class="nav nav-pills card-header-pills" id="settings-tabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="system-tab" data-bs-toggle="pill" data-bs-target="#system" type="button" role="tab">
                            <i class="bi bi-gear me-1"></i>系统设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="trading-tab" data-bs-toggle="pill" data-bs-target="#trading" type="button" role="tab">
                            <i class="bi bi-graph-up me-1"></i>交易设置
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="risk-tab" data-bs-toggle="pill" data-bs-target="#risk" type="button" role="tab">
                            <i class="bi bi-shield-check me-1"></i>风险控制
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="notification-tab" data-bs-toggle="pill" data-bs-target="#notification" type="button" role="tab">
                            <i class="bi bi-bell me-1"></i>通知设置
                        </button>
                    </li>
                </ul>
            </div>
            <div class="card-body">
                <div class="tab-content" id="settings-tab-content">
                    <!-- System Settings Tab -->
                    <div class="tab-pane fade show active" id="system" role="tabpanel">
                        <form id="system-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-3">基础配置</h6>
                                    
                                    <div class="mb-3">
                                        <label for="system-mode" class="form-label">系统模式</label>
                                        <select class="form-select" id="system-mode">
                                            <option value="simulation">模拟交易</option>
                                            <option value="live">实盘交易</option>
                                            <option value="backtest">回测模式</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="coordination-mode" class="form-label">协调模式</label>
                                        <select class="form-select" id="coordination-mode">
                                            <option value="autonomous">自主模式</option>
                                            <option value="collaborative" selected>协作模式</option>
                                            <option value="hierarchical">层级模式</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="update-interval" class="form-label">数据更新间隔 (秒)</label>
                                        <input type="number" class="form-control" id="update-interval" value="30" min="5" max="300">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="auto-start" checked>
                                            <label class="form-check-label" for="auto-start">
                                                系统启动时自动开始交易
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="mb-3">AI模型配置</h6>
                                    
                                    <div class="mb-3">
                                        <label for="ai-model" class="form-label">AI模型</label>
                                        <select class="form-select" id="ai-model">
                                            <option value="gpt-4" selected>GPT-4</option>
                                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                                            <option value="claude-3">Claude-3</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="temperature" class="form-label">创造性 (Temperature)</label>
                                        <input type="range" class="form-range" id="temperature" min="0" max="2" step="0.1" value="0.7">
                                        <div class="d-flex justify-content-between">
                                            <small>保守 (0)</small>
                                            <small id="temperature-value">0.7</small>
                                            <small>创新 (2)</small>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="max-tokens" class="form-label">最大Token数</label>
                                        <input type="number" class="form-control" id="max-tokens" value="2000" min="100" max="8000">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="debug-mode">
                                            <label class="form-check-label" for="debug-mode">
                                                启用调试模式
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Trading Settings Tab -->
                    <div class="tab-pane fade" id="trading" role="tabpanel">
                        <form id="trading-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-3">交易参数</h6>
                                    
                                    <div class="mb-3">
                                        <label for="initial-capital" class="form-label">初始资金 (元)</label>
                                        <input type="number" class="form-control" id="initial-capital" value="1000000" min="10000">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="max-position-size" class="form-label">最大单仓位比例 (%)</label>
                                        <input type="number" class="form-control" id="max-position-size" value="10" min="1" max="50">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="max-positions" class="form-label">最大持仓数量</label>
                                        <input type="number" class="form-control" id="max-positions" value="20" min="1" max="100">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="trading-frequency" class="form-label">交易频率</label>
                                        <select class="form-select" id="trading-frequency">
                                            <option value="high">高频 (分钟级)</option>
                                            <option value="medium" selected>中频 (小时级)</option>
                                            <option value="low">低频 (日级)</option>
                                        </select>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="mb-3">交易策略</h6>
                                    
                                    <div class="mb-3">
                                        <label for="strategy-type" class="form-label">主要策略</label>
                                        <select class="form-select" id="strategy-type">
                                            <option value="momentum">动量策略</option>
                                            <option value="mean-reversion">均值回归</option>
                                            <option value="trend-following" selected>趋势跟踪</option>
                                            <option value="arbitrage">套利策略</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="rebalance-frequency" class="form-label">再平衡频率</label>
                                        <select class="form-select" id="rebalance-frequency">
                                            <option value="daily">每日</option>
                                            <option value="weekly" selected>每周</option>
                                            <option value="monthly">每月</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="allow-short" checked>
                                            <label class="form-check-label" for="allow-short">
                                                允许做空
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="use-leverage">
                                            <label class="form-check-label" for="use-leverage">
                                                使用杠杆
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Risk Control Tab -->
                    <div class="tab-pane fade" id="risk" role="tabpanel">
                        <form id="risk-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-3">风险限制</h6>
                                    
                                    <div class="mb-3">
                                        <label for="stop-loss" class="form-label">止损比例 (%)</label>
                                        <input type="number" class="form-control" id="stop-loss" value="5" min="1" max="20" step="0.1">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="take-profit" class="form-label">止盈比例 (%)</label>
                                        <input type="number" class="form-control" id="take-profit" value="15" min="5" max="50" step="0.1">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="max-drawdown" class="form-label">最大回撤限制 (%)</label>
                                        <input type="number" class="form-control" id="max-drawdown" value="10" min="5" max="30" step="0.1">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="var-limit" class="form-label">VaR限制 (%)</label>
                                        <input type="number" class="form-control" id="var-limit" value="2" min="0.5" max="10" step="0.1">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="mb-3">风险监控</h6>
                                    
                                    <div class="mb-3">
                                        <label for="risk-check-interval" class="form-label">风险检查间隔 (分钟)</label>
                                        <input type="number" class="form-control" id="risk-check-interval" value="5" min="1" max="60">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="auto-stop-loss" checked>
                                            <label class="form-check-label" for="auto-stop-loss">
                                                自动止损
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="emergency-stop" checked>
                                            <label class="form-check-label" for="emergency-stop">
                                                紧急停止机制
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="risk-alerts" checked>
                                            <label class="form-check-label" for="risk-alerts">
                                                风险预警通知
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!-- Notification Settings Tab -->
                    <div class="tab-pane fade" id="notification" role="tabpanel">
                        <form id="notification-settings-form">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="mb-3">通知方式</h6>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="email-notifications" checked>
                                            <label class="form-check-label" for="email-notifications">
                                                邮件通知
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="email-address" class="form-label">邮箱地址</label>
                                        <input type="email" class="form-control" id="email-address" placeholder="<EMAIL>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" id="sms-notifications">
                                            <label class="form-check-label" for="sms-notifications">
                                                短信通知
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="phone-number" class="form-label">手机号码</label>
                                        <input type="tel" class="form-control" id="phone-number" placeholder="+86 138 0000 0000">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6 class="mb-3">通知内容</h6>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="trade-notifications" checked>
                                            <label class="form-check-label" for="trade-notifications">
                                                交易执行通知
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="risk-notifications" checked>
                                            <label class="form-check-label" for="risk-notifications">
                                                风险预警通知
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="system-notifications" checked>
                                            <label class="form-check-label" for="system-notifications">
                                                系统状态通知
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="market-notifications">
                                            <label class="form-check-label" for="market-notifications">
                                                市场重要事件通知
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                            <i class="bi bi-arrow-clockwise"></i> 重置默认
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="exportSettings()">
                            <i class="bi bi-download"></i> 导出配置
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="importSettings()">
                            <i class="bi bi-upload"></i> 导入配置
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary me-2" onclick="cancelChanges()">
                            取消
                        </button>
                        <button type="button" class="btn btn-primary" onclick="saveSettings()">
                            <i class="bi bi-check-lg"></i> 保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadCurrentSettings();
    setupEventHandlers();
});

function setupEventHandlers() {
    // Temperature slider
    const temperatureSlider = document.getElementById('temperature');
    const temperatureValue = document.getElementById('temperature-value');
    
    temperatureSlider.addEventListener('input', function() {
        temperatureValue.textContent = this.value;
    });
}

function loadCurrentSettings() {
    // In a real implementation, this would load from the backend
    // For now, we'll use the default values already set in the HTML
    showAlert('info', '设置已加载');
}

function saveSettings() {
    // Collect all form data
    const systemSettings = collectFormData('system-settings-form');
    const tradingSettings = collectFormData('trading-settings-form');
    const riskSettings = collectFormData('risk-settings-form');
    const notificationSettings = collectFormData('notification-settings-form');
    
    const allSettings = {
        system: systemSettings,
        trading: tradingSettings,
        risk: riskSettings,
        notification: notificationSettings
    };
    
    // In a real implementation, this would send to the backend
    console.log('Saving settings:', allSettings);
    showAlert('success', '设置已保存');
}

function collectFormData(formId) {
    const form = document.getElementById(formId);
    if (!form) return {};
    
    const formData = new FormData(form);
    const data = {};
    
    // Get all form elements
    const elements = form.querySelectorAll('input, select, textarea');
    elements.forEach(element => {
        if (element.type === 'checkbox') {
            data[element.id] = element.checked;
        } else if (element.type === 'radio') {
            if (element.checked) {
                data[element.name] = element.value;
            }
        } else {
            data[element.id] = element.value;
        }
    });
    
    return data;
}

function resetSettings() {
    if (confirm('确定要重置所有设置为默认值吗？')) {
        // Reset all forms to default values
        document.querySelectorAll('form').forEach(form => {
            form.reset();
        });
        
        // Reset specific values
        document.getElementById('temperature').value = '0.7';
        document.getElementById('temperature-value').textContent = '0.7';
        
        showAlert('warning', '设置已重置为默认值');
    }
}

function exportSettings() {
    const systemSettings = collectFormData('system-settings-form');
    const tradingSettings = collectFormData('trading-settings-form');
    const riskSettings = collectFormData('risk-settings-form');
    const notificationSettings = collectFormData('notification-settings-form');
    
    const allSettings = {
        system: systemSettings,
        trading: tradingSettings,
        risk: riskSettings,
        notification: notificationSettings,
        exportDate: new Date().toISOString()
    };
    
    // Create and download JSON file
    const dataStr = JSON.stringify(allSettings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `stock-ai-settings-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    showAlert('success', '设置已导出');
}

function importSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const settings = JSON.parse(e.target.result);
                applySettings(settings);
                showAlert('success', '设置已导入');
            } catch (error) {
                showAlert('danger', '导入失败：文件格式错误');
            }
        };
        reader.readAsText(file);
    };
    
    input.click();
}

function applySettings(settings) {
    // Apply system settings
    if (settings.system) {
        Object.keys(settings.system).forEach(key => {
            const element = document.getElementById(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = settings.system[key];
                } else {
                    element.value = settings.system[key];
                }
            }
        });
    }
    
    // Apply other settings similarly...
    // (Implementation would continue for trading, risk, and notification settings)
}

function cancelChanges() {
    if (confirm('确定要取消所有未保存的更改吗？')) {
        loadCurrentSettings();
        showAlert('info', '更改已取消');
    }
}

// Override the global refresh function
async function refreshData() {
    loadCurrentSettings();
}
</script>
{% endblock %}
