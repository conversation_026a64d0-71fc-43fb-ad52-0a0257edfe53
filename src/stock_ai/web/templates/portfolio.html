{% extends "base.html" %}

{% block title %}投资组合 - Stock AI Agents{% endblock %}

{% block page_title %}投资组合{% endblock %}
{% block page_description %}查看和管理投资组合表现{% endblock %}

{% block content %}
<!-- Portfolio Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">总资产</h6>
                        <h3 id="total-value">¥0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-wallet2 fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">现金余额</h6>
                        <h3 id="cash-balance">¥0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-cash fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">今日盈亏</h6>
                        <h3 id="daily-pnl">¥0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-up fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">总盈亏</h6>
                        <h3 id="total-pnl">¥0</h3>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-trophy fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Portfolio Performance Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-graph-up me-2"></i>投资组合表现</h5>
                    <div>
                        <div class="btn-group btn-group-sm" role="group">
                            <input type="radio" class="btn-check" name="timeRange" id="range1d" value="1d">
                            <label class="btn btn-outline-primary" for="range1d">1天</label>
                            
                            <input type="radio" class="btn-check" name="timeRange" id="range1w" value="1w">
                            <label class="btn btn-outline-primary" for="range1w">1周</label>
                            
                            <input type="radio" class="btn-check" name="timeRange" id="range1m" value="1m" checked>
                            <label class="btn btn-outline-primary" for="range1m">1月</label>
                            
                            <input type="radio" class="btn-check" name="timeRange" id="range3m" value="3m">
                            <label class="btn btn-outline-primary" for="range3m">3月</label>
                            
                            <input type="radio" class="btn-check" name="timeRange" id="range1y" value="1y">
                            <label class="btn btn-outline-primary" for="range1y">1年</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <canvas id="performanceChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Holdings and Allocation -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-list-ul me-2"></i>持仓明细</h5>
            </div>
            <div class="card-body">
                <div id="positions-table">
                    <div class="text-center">
                        <div class="loading-spinner"></div>
                        <p class="mt-2">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-pie-chart me-2"></i>资产配置</h5>
            </div>
            <div class="card-body">
                <canvas id="allocationChart"></canvas>
                <div class="mt-3" id="allocation-legend">
                    <!-- Legend will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Risk Metrics -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-shield-check me-2"></i>风险指标</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-primary">15.2%</h4>
                            <p class="text-muted mb-0">年化波动率</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-success">1.35</h4>
                            <p class="text-muted mb-0">夏普比率</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-warning">-8.5%</h4>
                            <p class="text-muted mb-0">最大回撤</p>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <h4 class="text-info">0.85</h4>
                            <p class="text-muted mb-0">Beta系数</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let performanceChart, allocationChart;
let portfolioData = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    loadPortfolioData();
    initCharts();
    setupTimeRangeButtons();
});

async function loadPortfolioData() {
    try {
        // Load portfolio summary
        const portfolioResponse = await fetch('/api/portfolio');
        portfolioData = await portfolioResponse.json();
        updatePortfolioSummary(portfolioData);

        // Load positions
        const positionsResponse = await fetch('/api/portfolio/positions');
        const positionsData = await positionsResponse.json();
        updatePositionsTable(positionsData);
        updateAllocationChart(positionsData);

    } catch (error) {
        console.error('Error loading portfolio data:', error);
        showAlert('danger', '加载投资组合数据失败: ' + error.message);
    }
}

function updatePortfolioSummary(data) {
    document.getElementById('total-value').textContent = formatCurrency(data.total_value);
    document.getElementById('cash-balance').textContent = formatCurrency(data.cash_balance);

    const dailyPnlElement = document.getElementById('daily-pnl');
    dailyPnlElement.textContent = formatCurrency(data.daily_pnl);
    dailyPnlElement.className = data.daily_pnl >= 0 ? 'text-success' : 'text-danger';

    const totalPnlElement = document.getElementById('total-pnl');
    totalPnlElement.textContent = formatCurrency(data.total_pnl);
    totalPnlElement.className = data.total_pnl >= 0 ? 'text-success' : 'text-danger';
}

function updatePositionsTable(data) {
    const container = document.getElementById('positions-table');

    if (!data.positions || data.positions.length === 0) {
        container.innerHTML = '<p class="text-muted text-center">暂无持仓数据</p>';
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>股票</th>
                        <th>数量</th>
                        <th>成本价</th>
                        <th>现价</th>
                        <th>市值</th>
                        <th>盈亏</th>
                        <th>盈亏率</th>
                        <th>权重</th>
                    </tr>
                </thead>
                <tbody>
    `;

    data.positions.forEach(position => {
        const pnlClass = position.pnl >= 0 ? 'text-success' : 'text-danger';
        const pnlPercentClass = position.pnl_percent >= 0 ? 'text-success' : 'text-danger';

        html += `
            <tr>
                <td>
                    <div>
                        <div class="fw-bold">${position.symbol}</div>
                        <small class="text-muted">${position.name}</small>
                    </div>
                </td>
                <td>${formatNumber(position.quantity)}</td>
                <td>¥${position.avg_price.toFixed(2)}</td>
                <td>¥${position.current_price.toFixed(2)}</td>
                <td>${formatCurrency(position.market_value)}</td>
                <td class="${pnlClass}">${formatCurrency(position.pnl)}</td>
                <td class="${pnlPercentClass}">${formatPercent(position.pnl_percent)}</td>
                <td>
                    <div class="progress" style="height: 20px;">
                        <div class="progress-bar" role="progressbar"
                             style="width: ${position.weight}%"
                             aria-valuenow="${position.weight}"
                             aria-valuemin="0" aria-valuemax="100">
                            ${position.weight.toFixed(1)}%
                        </div>
                    </div>
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

// Override the global refresh function
async function refreshData() {
    await loadPortfolioData();
}
</script>
{% endblock %}
