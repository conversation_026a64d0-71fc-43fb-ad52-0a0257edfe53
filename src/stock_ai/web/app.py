"""
FastAPI web application for Stock AI Agents system
"""

import asyncio
from pathlib import Path
from typing import Optional

from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from ..config import settings
from ..services import AgentManager, CoordinationService, SchedulerService
from .routes import router


# Global services
agent_manager: Optional[AgentManager] = None
coordination_service: Optional[CoordinationService] = None
scheduler_service: Optional[SchedulerService] = None


def create_app() -> FastAPI:
    """Create FastAPI application"""
    
    app = FastAPI(
        title="Stock AI Agents Dashboard",
        description="Multi-agent stock trading system with AI-powered decision making",
        version="1.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # In production, specify allowed origins
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Setup static files and templates
    web_dir = Path(__file__).parent
    static_dir = web_dir / "static"
    templates_dir = web_dir / "templates"
    
    # Create directories if they don't exist
    static_dir.mkdir(exist_ok=True)
    templates_dir.mkdir(exist_ok=True)
    
    # Mount static files
    app.mount("/static", StaticFiles(directory=str(static_dir)), name="static")
    
    # Setup templates
    templates = Jinja2Templates(directory=str(templates_dir))
    
    # Include API routes
    app.include_router(router, prefix="/api")
    
    @app.get("/", response_class=HTMLResponse)
    async def dashboard(request: Request):
        """Main dashboard page"""
        return templates.TemplateResponse("dashboard.html", {"request": request})
    
    @app.get("/agents", response_class=HTMLResponse)
    async def agents_page(request: Request):
        """Agents management page"""
        return templates.TemplateResponse("agents.html", {"request": request})
    
    @app.get("/portfolio", response_class=HTMLResponse)
    async def portfolio_page(request: Request):
        """Portfolio overview page"""
        return templates.TemplateResponse("portfolio.html", {"request": request})
    
    @app.get("/market", response_class=HTMLResponse)
    async def market_page(request: Request):
        """Market analysis page"""
        return templates.TemplateResponse("market.html", {"request": request})
    
    @app.get("/settings", response_class=HTMLResponse)
    async def settings_page(request: Request):
        """Settings page"""
        return templates.TemplateResponse("settings.html", {"request": request})
    
    @app.on_event("startup")
    async def startup_event():
        """Initialize services on startup"""
        global agent_manager, coordination_service, scheduler_service
        
        try:
            logger.info("Initializing web application services...")
            
            # Initialize services
            agent_manager = AgentManager()
            coordination_service = CoordinationService(agent_manager)
            scheduler_service = SchedulerService(agent_manager, coordination_service)
            
            # Initialize agent manager
            await agent_manager.initialize()
            
            logger.info("Web application services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize web application services: {e}")
            raise
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Cleanup services on shutdown"""
        global agent_manager, coordination_service, scheduler_service
        
        try:
            logger.info("Shutting down web application services...")
            
            if scheduler_service:
                await scheduler_service.stop()
            
            if coordination_service:
                await coordination_service.stop()
            
            if agent_manager:
                await agent_manager.stop_all_agents()
                await agent_manager.cleanup()
            
            logger.info("Web application services shut down successfully")
            
        except Exception as e:
            logger.error(f"Error during web application shutdown: {e}")
    
    return app


# Create the app instance
app = create_app()


def get_services():
    """Get current service instances"""
    return {
        "agent_manager": agent_manager,
        "coordination_service": coordination_service,
        "scheduler_service": scheduler_service,
    }
