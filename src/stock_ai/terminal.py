"""
Interactive terminal interface for Stock AI Agents system
"""

import asyncio
import sys
from typing import Dict, Any, Optional, List
from datetime import datetime

import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich.prompt import Prompt, Confirm
from rich.live import Live
from rich.layout import Layout
from rich.align import Align
from loguru import logger

from .services import AgentManager, CoordinationService, SchedulerService
from .utils.display import create_status_display
from .config import settings


class InteractiveTerminal:
    """Interactive terminal interface"""
    
    def __init__(self):
        self.console = Console()
        self.agent_manager: Optional[AgentManager] = None
        self.coordination_service: Optional[CoordinationService] = None
        self.scheduler_service: Optional[SchedulerService] = None
        self.is_running = False
        self.live_display = None
        
    async def start(self) -> None:
        """Start interactive terminal"""
        try:
            self.console.print(Panel.fit(
                "[bold blue]🤖 Stock AI Agents - Interactive Terminal[/bold blue]\n"
                "Multi-agent stock trading system with AI-powered decision making",
                title="Welcome",
                border_style="blue"
            ))
            
            # Initialize services
            await self._initialize_services()
            
            # Start main loop
            await self._main_loop()
            
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Exiting...[/yellow]")
        except Exception as e:
            self.console.print(f"[bold red]Terminal error: {e}[/bold red]")
            logger.exception("Terminal error")
        finally:
            await self._cleanup()
    
    async def _initialize_services(self) -> None:
        """Initialize all services"""
        try:
            self.console.print("[dim]Initializing services...[/dim]")
            
            # Initialize agent manager
            self.agent_manager = AgentManager()
            await self.agent_manager.initialize()
            
            # Initialize coordination service
            self.coordination_service = CoordinationService(self.agent_manager)
            
            # Initialize scheduler service
            self.scheduler_service = SchedulerService(self.agent_manager, self.coordination_service)
            
            self.console.print("[green]✓ Services initialized[/green]")
            
        except Exception as e:
            self.console.print(f"[red]✗ Failed to initialize services: {e}[/red]")
            raise
    
    async def _main_loop(self) -> None:
        """Main interactive loop"""
        while True:
            try:
                # Show main menu
                choice = self._show_main_menu()
                
                if choice == "1":
                    await self._start_system()
                elif choice == "2":
                    await self._stop_system()
                elif choice == "3":
                    await self._pause_resume_system()
                elif choice == "4":
                    await self._show_status()
                elif choice == "5":
                    await self._show_live_dashboard()
                elif choice == "6":
                    await self._agent_management()
                elif choice == "7":
                    await self._coordination_management()
                elif choice == "8":
                    await self._view_logs()
                elif choice == "9":
                    await self._system_settings()
                elif choice == "0":
                    break
                else:
                    self.console.print("[red]Invalid choice. Please try again.[/red]")
                
                # Pause before showing menu again
                if choice != "5":  # Don't pause after live dashboard
                    self.console.print("\n[dim]Press Enter to continue...[/dim]")
                    input()
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")
                logger.exception("Main loop error")
    
    def _show_main_menu(self) -> str:
        """Show main menu and get user choice"""
        self.console.clear()
        
        menu_table = Table(title="🤖 Stock AI Agents - Main Menu", show_header=False)
        menu_table.add_column("Option", style="cyan", width=4)
        menu_table.add_column("Description", style="white")
        
        menu_items = [
            ("1", "🚀 Start System"),
            ("2", "🛑 Stop System"),
            ("3", "⏸️  Pause/Resume System"),
            ("4", "📊 Show Status"),
            ("5", "📈 Live Dashboard"),
            ("6", "🤖 Agent Management"),
            ("7", "🔗 Coordination Management"),
            ("8", "📋 View Logs"),
            ("9", "⚙️  System Settings"),
            ("0", "🚪 Exit"),
        ]
        
        for option, description in menu_items:
            menu_table.add_row(option, description)
        
        self.console.print(menu_table)
        self.console.print()
        
        return Prompt.ask("Select an option", choices=[str(i) for i in range(10)])
    
    async def _start_system(self) -> None:
        """Start the system"""
        try:
            if self.is_running:
                self.console.print("[yellow]System is already running[/yellow]")
                return
            
            self.console.print("[blue]Starting Stock AI Agents system...[/blue]")
            
            # Start services
            await self.agent_manager.start_all_agents()
            await self.coordination_service.start()
            await self.scheduler_service.start()
            
            self.is_running = True
            
            self.console.print("[green]✓ System started successfully![/green]")
            self.console.print(f"[dim]Trading mode: {settings.trading.mode}[/dim]")
            
        except Exception as e:
            self.console.print(f"[red]✗ Failed to start system: {e}[/red]")
            logger.exception("Failed to start system")
    
    async def _stop_system(self) -> None:
        """Stop the system"""
        try:
            if not self.is_running:
                self.console.print("[yellow]System is not running[/yellow]")
                return
            
            if not Confirm.ask("Are you sure you want to stop the system?"):
                return
            
            self.console.print("[blue]Stopping Stock AI Agents system...[/blue]")
            
            # Stop services
            await self.scheduler_service.stop()
            await self.coordination_service.stop()
            await self.agent_manager.stop_all_agents()
            
            self.is_running = False
            
            self.console.print("[green]✓ System stopped successfully![/green]")
            
        except Exception as e:
            self.console.print(f"[red]✗ Failed to stop system: {e}[/red]")
            logger.exception("Failed to stop system")
    
    async def _pause_resume_system(self) -> None:
        """Pause or resume the system"""
        try:
            if not self.is_running:
                self.console.print("[yellow]System is not running[/yellow]")
                return
            
            status = self.scheduler_service.get_status()
            current_state = status.get("system_state", "UNKNOWN")
            
            self.console.print(f"Current system state: [bold]{current_state}[/bold]")
            
            if current_state in ["RUNNING"]:
                if Confirm.ask("Do you want to pause the system?"):
                    reason = Prompt.ask("Reason for pause", default="Manual pause")
                    success = await self.scheduler_service.pause_system(reason)
                    if success:
                        self.console.print("[yellow]✓ System paused[/yellow]")
                    else:
                        self.console.print("[red]✗ Failed to pause system[/red]")
            
            elif current_state in ["PAUSED", "MANUAL_PAUSE", "MARKET_CLOSED_PAUSE"]:
                if Confirm.ask("Do you want to resume the system?"):
                    reason = Prompt.ask("Reason for resume", default="Manual resume")
                    success = await self.scheduler_service.resume_system(reason)
                    if success:
                        self.console.print("[green]✓ System resumed[/green]")
                    else:
                        self.console.print("[red]✗ Failed to resume system[/red]")
            
            elif current_state == "EMERGENCY_STOP":
                self.console.print("[red]System is in emergency stop state[/red]")
                if Confirm.ask("Do you want to clear emergency stop and resume?"):
                    reason = Prompt.ask("Reason for resume", default="Emergency cleared")
                    success = await self.scheduler_service.resume_system(reason)
                    if success:
                        self.console.print("[green]✓ Emergency stop cleared, system resumed[/green]")
                    else:
                        self.console.print("[red]✗ Failed to clear emergency stop[/red]")
            
        except Exception as e:
            self.console.print(f"[red]Error managing system state: {e}[/red]")
            logger.exception("Error managing system state")
    
    async def _show_status(self) -> None:
        """Show system status"""
        try:
            self.console.clear()
            self.console.print("[bold blue]📊 System Status[/bold blue]\n")
            
            # System overview
            overview_table = Table(title="System Overview")
            overview_table.add_column("Component", style="cyan")
            overview_table.add_column("Status", style="green")
            overview_table.add_column("Details")
            
            # Agent Manager status
            if self.agent_manager:
                agent_status = self.agent_manager.get_agent_status()
                overview_table.add_row(
                    "Agent Manager",
                    "🟢 Running" if agent_status.get("manager_running") else "🔴 Stopped",
                    f"{agent_status.get('total_agents', 0)} agents, {agent_status.get('active_tasks', 0)} active"
                )
            
            # Coordination Service status
            if self.coordination_service:
                coord_status = self.coordination_service.get_coordination_status()
                overview_table.add_row(
                    "Coordination Service",
                    "🟢 Running" if coord_status.get("is_running") else "🔴 Stopped",
                    f"Mode: {coord_status.get('coordination_mode', 'UNKNOWN')}"
                )
            
            # Scheduler Service status
            if self.scheduler_service:
                sched_status = self.scheduler_service.get_status()
                overview_table.add_row(
                    "Scheduler Service",
                    "🟢 Running" if sched_status.get("is_running") else "🔴 Stopped",
                    f"State: {sched_status.get('system_state', 'UNKNOWN')}"
                )
            
            self.console.print(overview_table)
            self.console.print()
            
            # Agent details
            if self.agent_manager:
                agents_table = Table(title="Agent Details")
                agents_table.add_column("Agent", style="cyan")
                agents_table.add_column("Status")
                agents_table.add_column("Executions", justify="right")
                agents_table.add_column("Errors", justify="right")
                agents_table.add_column("Last Execution")
                
                agent_status = self.agent_manager.get_agent_status()
                agents = agent_status.get("agents", {})
                
                for agent_name, status in agents.items():
                    status_emoji = {
                        "ACTIVE": "🟢",
                        "INACTIVE": "⚫",
                        "PAUSED": "🟡",
                        "ERROR": "🔴"
                    }.get(status.get("status", "UNKNOWN"), "❓")
                    
                    last_exec = status.get("last_execution", "Never")
                    if last_exec != "Never":
                        try:
                            last_exec_dt = datetime.fromisoformat(last_exec.replace('Z', '+00:00'))
                            last_exec = last_exec_dt.strftime("%H:%M:%S")
                        except:
                            last_exec = "Invalid"
                    
                    agents_table.add_row(
                        agent_name.replace("_", " ").title(),
                        f"{status_emoji} {status.get('status', 'UNKNOWN')}",
                        str(status.get("execution_count", 0)),
                        str(status.get("error_count", 0)),
                        last_exec
                    )
                
                self.console.print(agents_table)
            
        except Exception as e:
            self.console.print(f"[red]Error showing status: {e}[/red]")
            logger.exception("Error showing status")
    
    async def _show_live_dashboard(self) -> None:
        """Show live dashboard"""
        try:
            self.console.print("[blue]Starting live dashboard... Press Ctrl+C to exit[/blue]")
            
            with Live(
                create_status_display(self.agent_manager, self.coordination_service),
                refresh_per_second=1,
                console=self.console
            ) as live:
                try:
                    while True:
                        live.update(create_status_display(self.agent_manager, self.coordination_service))
                        await asyncio.sleep(1)
                except KeyboardInterrupt:
                    pass
            
            self.console.print("[green]Live dashboard stopped[/green]")
            
        except Exception as e:
            self.console.print(f"[red]Error in live dashboard: {e}[/red]")
            logger.exception("Error in live dashboard")
    
    async def _agent_management(self) -> None:
        """Agent management menu"""
        try:
            while True:
                self.console.clear()
                self.console.print("[bold blue]🤖 Agent Management[/bold blue]\n")
                
                # Show agent list
                if self.agent_manager:
                    agent_status = self.agent_manager.get_agent_status()
                    agents = agent_status.get("agents", {})
                    
                    agents_table = Table()
                    agents_table.add_column("ID", style="cyan", width=4)
                    agents_table.add_column("Agent", style="white")
                    agents_table.add_column("Status")
                    
                    agent_list = list(agents.items())
                    for i, (agent_name, status) in enumerate(agent_list, 1):
                        status_emoji = {
                            "ACTIVE": "🟢",
                            "INACTIVE": "⚫",
                            "PAUSED": "🟡",
                            "ERROR": "🔴"
                        }.get(status.get("status", "UNKNOWN"), "❓")
                        
                        agents_table.add_row(
                            str(i),
                            agent_name.replace("_", " ").title(),
                            f"{status_emoji} {status.get('status', 'UNKNOWN')}"
                        )
                    
                    self.console.print(agents_table)
                    self.console.print()
                
                # Menu options
                self.console.print("Options:")
                self.console.print("1. Pause agent")
                self.console.print("2. Resume agent")
                self.console.print("3. Send message to agent")
                self.console.print("4. View agent details")
                self.console.print("0. Back to main menu")
                
                choice = Prompt.ask("Select option", choices=["0", "1", "2", "3", "4"])
                
                if choice == "0":
                    break
                elif choice in ["1", "2", "3", "4"]:
                    await self._handle_agent_action(choice, agent_list)
                
        except Exception as e:
            self.console.print(f"[red]Error in agent management: {e}[/red]")
            logger.exception("Error in agent management")
    
    async def _handle_agent_action(self, action: str, agent_list: List) -> None:
        """Handle agent management actions"""
        try:
            if not agent_list:
                self.console.print("[yellow]No agents available[/yellow]")
                return
            
            # Get agent selection
            agent_id = Prompt.ask(
                "Select agent ID",
                choices=[str(i) for i in range(1, len(agent_list) + 1)]
            )
            agent_name = agent_list[int(agent_id) - 1][0]
            
            if action == "1":  # Pause agent
                success = await self.agent_manager.pause_agent(agent_name)
                if success:
                    self.console.print(f"[green]✓ Agent {agent_name} paused[/green]")
                else:
                    self.console.print(f"[red]✗ Failed to pause agent {agent_name}[/red]")
            
            elif action == "2":  # Resume agent
                success = await self.agent_manager.resume_agent(agent_name)
                if success:
                    self.console.print(f"[green]✓ Agent {agent_name} resumed[/green]")
                else:
                    self.console.print(f"[red]✗ Failed to resume agent {agent_name}[/red]")
            
            elif action == "3":  # Send message
                message = Prompt.ask("Enter message to send")
                response = await self.agent_manager.send_message_to_agent(agent_name, message, "terminal_user")
                
                if "error" in response:
                    self.console.print(f"[red]✗ Error: {response['error']}[/red]")
                else:
                    self.console.print(f"[green]✓ Message sent to {agent_name}[/green]")
                    self.console.print(f"[blue]Response:[/blue] {response.get('response', 'No response')}")
            
            elif action == "4":  # View details
                status = self.agent_manager.get_agent_status(agent_name)
                
                details_table = Table(title=f"Agent Details: {agent_name}")
                details_table.add_column("Property", style="cyan")
                details_table.add_column("Value")
                
                for key, value in status.items():
                    if key != "available_tools":
                        details_table.add_row(key.replace("_", " ").title(), str(value))
                
                self.console.print(details_table)
                
                # Show available tools
                tools = status.get("available_tools", [])
                if tools:
                    self.console.print(f"\n[blue]Available Tools:[/blue] {', '.join(tools)}")
            
            input("\nPress Enter to continue...")
            
        except Exception as e:
            self.console.print(f"[red]Error handling agent action: {e}[/red]")
            logger.exception("Error handling agent action")
    
    async def _coordination_management(self) -> None:
        """Coordination management menu"""
        try:
            self.console.clear()
            self.console.print("[bold blue]🔗 Coordination Management[/bold blue]\n")
            
            if not self.coordination_service:
                self.console.print("[red]Coordination service not available[/red]")
                return
            
            status = self.coordination_service.get_coordination_status()
            
            # Show current status
            status_table = Table(title="Coordination Status")
            status_table.add_column("Property", style="cyan")
            status_table.add_column("Value")
            
            status_table.add_row("Running", "🟢 Yes" if status.get("is_running") else "🔴 No")
            status_table.add_row("Mode", status.get("coordination_mode", "UNKNOWN"))
            status_table.add_row("Interval", f"{status.get('coordination_interval', 0)}s")
            status_table.add_row("Last Coordination", status.get("last_coordination", "Never"))
            
            self.console.print(status_table)
            self.console.print()
            
            # Menu options
            self.console.print("Options:")
            self.console.print("1. Trigger coordination now")
            self.console.print("2. Change coordination mode")
            self.console.print("3. Change coordination interval")
            self.console.print("4. View coordination history")
            self.console.print("0. Back to main menu")
            
            choice = Prompt.ask("Select option", choices=["0", "1", "2", "3", "4"])
            
            if choice == "1":
                result = await self.agent_manager.coordinate_agents()
                if "error" in result:
                    self.console.print(f"[red]✗ Coordination failed: {result['error']}[/red]")
                else:
                    self.console.print("[green]✓ Coordination completed[/green]")
            
            elif choice == "2":
                from .services.coordination_service import CoordinationMode
                modes = [mode.value for mode in CoordinationMode]
                new_mode = Prompt.ask("Select coordination mode", choices=modes)
                self.coordination_service.set_coordination_mode(CoordinationMode(new_mode))
                self.console.print(f"[green]✓ Coordination mode set to {new_mode}[/green]")
            
            elif choice == "3":
                interval = int(Prompt.ask("Enter coordination interval (seconds)", default="600"))
                self.coordination_service.set_coordination_interval(interval)
                self.console.print(f"[green]✓ Coordination interval set to {interval}s[/green]")
            
            elif choice == "4":
                history = self.coordination_service.get_coordination_history(5)
                if history:
                    for i, event in enumerate(history, 1):
                        self.console.print(f"[blue]{i}.[/blue] {event.get('timestamp', 'Unknown time')}")
                        self.console.print(f"   Mode: {event.get('mode', 'Unknown')}")
                        actions = event.get('actions_taken', [])
                        if actions:
                            self.console.print(f"   Actions: {', '.join(actions)}")
                        self.console.print()
                else:
                    self.console.print("[yellow]No coordination history available[/yellow]")
            
            if choice != "0":
                input("\nPress Enter to continue...")
            
        except Exception as e:
            self.console.print(f"[red]Error in coordination management: {e}[/red]")
            logger.exception("Error in coordination management")
    
    async def _view_logs(self) -> None:
        """View system logs"""
        self.console.print("[blue]Log viewing functionality would be implemented here[/blue]")
        self.console.print("[dim]This would show recent log entries from different log files[/dim]")
    
    async def _system_settings(self) -> None:
        """System settings menu"""
        self.console.print("[blue]System settings functionality would be implemented here[/blue]")
        self.console.print("[dim]This would allow changing configuration parameters[/dim]")
    
    async def _cleanup(self) -> None:
        """Cleanup resources"""
        try:
            if self.is_running:
                await self._stop_system()
        except Exception as e:
            logger.exception("Error during cleanup")


async def run_interactive_terminal():
    """Run the interactive terminal"""
    terminal = InteractiveTerminal()
    await terminal.start()


if __name__ == "__main__":
    asyncio.run(run_interactive_terminal())
