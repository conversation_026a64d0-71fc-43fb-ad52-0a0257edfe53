"""
Portfolio Manager Agent - Manages overall portfolio allocation and performance
"""

from datetime import datetime
from typing import Dict, List, Any

from loguru import logger

from .base import BaseAgent, AgentConfig
from ..models.agent import AgentType
from ..tools import PortfolioAnalysisTool, PlanManagerTool, AccountTool, NotificationTool
from ..config import settings


PORTFOLIO_MANAGER_SYSTEM_PROMPT = """You are a Portfolio Manager AI agent responsible for overall portfolio optimization and strategic asset allocation.

## Your Role and Responsibilities

As the Portfolio Manager, you are responsible for:

1. **Portfolio Optimization**: Optimize asset allocation for risk-adjusted returns
2. **Performance Monitoring**: Track portfolio performance against benchmarks and goals
3. **Rebalancing**: Identify and execute portfolio rebalancing opportunities
4. **Strategic Planning**: Develop and maintain investment plans and strategies
5. **Risk Management**: Ensure portfolio stays within risk parameters
6. **Reporting**: Provide regular performance updates and insights

## Your Management Framework

### Portfolio Analysis
- Monitor overall portfolio performance and metrics
- Analyze asset allocation and diversification
- Track risk-adjusted returns and Sharpe ratios
- Compare performance against benchmarks

### Strategic Planning
- Develop investment plans with clear objectives
- Set target allocations by sector and asset class
- Define risk parameters and constraints
- Establish performance goals and timelines

### Rebalancing Strategy
- Identify overweight and underweight positions
- Recommend rebalancing actions to maintain target allocation
- Consider tax implications and transaction costs
- Time rebalancing with market conditions

### Performance Reporting
- Generate regular performance reports
- Analyze attribution of returns by sector and position
- Identify top performers and underperformers
- Provide insights for strategy improvement

## Your Decision Criteria

- Maintain diversification across sectors and positions
- Keep individual positions within size limits (typically 10-15%)
- Ensure adequate cash reserves (5-15% of portfolio)
- Optimize for risk-adjusted returns, not just absolute returns
- Consider correlation between positions
- Align with overall investment strategy and goals

Remember: Your role is strategic oversight. Focus on the big picture while ensuring tactical decisions align with long-term objectives.
"""


class PortfolioManagerAgent(BaseAgent):
    """Portfolio Manager Agent implementation"""
    
    def __init__(self):
        tools = [
            PortfolioAnalysisTool(),
            PlanManagerTool(),
            AccountTool(),
            NotificationTool(),
        ]
        
        config = AgentConfig(
            name="portfolio_manager",
            agent_type=AgentType.PORTFOLIO_MANAGER,
            description="Manages overall portfolio allocation and performance",
            system_prompt=PORTFOLIO_MANAGER_SYSTEM_PROMPT,
            model_name=settings.openai.model,
            temperature=0.3,
            max_tokens=2000,
            tools=tools,
            update_interval=1800,  # 30 minutes
        )
        
        super().__init__(config)
        self.current_plan = None
        self.last_rebalancing = None
    
    async def execute_cycle(self) -> Dict[str, Any]:
        """Execute portfolio management cycle"""
        try:
            logger.info(f"Portfolio Manager {self.name} starting management cycle")
            
            # Step 1: Analyze current portfolio
            portfolio_analysis = await self.analyze_portfolio()
            
            # Step 2: Check rebalancing needs
            rebalancing_analysis = await self.check_rebalancing_needs()
            
            # Step 3: Update investment plans
            plan_updates = await self.update_investment_plans(portfolio_analysis)
            
            # Step 4: Generate performance report
            performance_report = await self.generate_performance_report(portfolio_analysis)
            
            result = {
                "cycle_completed": True,
                "portfolio_analysis": portfolio_analysis,
                "rebalancing_analysis": rebalancing_analysis,
                "plan_updates": plan_updates,
                "performance_report": performance_report,
                "timestamp": datetime.now().isoformat(),
            }
            
            logger.info(f"Portfolio Manager {self.name} completed management cycle")
            return result
            
        except Exception as e:
            logger.error(f"Portfolio Manager {self.name} cycle failed: {e}")
            raise
    
    async def analyze_portfolio(self) -> Dict[str, Any]:
        """Analyze current portfolio performance and allocation"""
        try:
            # Get portfolio performance analysis
            performance_result = await self.execute_tool(
                "portfolio_analysis_tool",
                analysis_type="performance",
                account_name="default",
                period="3mo"
            )
            
            # Get allocation analysis
            allocation_result = await self.execute_tool(
                "portfolio_analysis_tool",
                analysis_type="allocation",
                account_name="default"
            )
            
            return {
                "performance": performance_result.data if performance_result.status.value == "SUCCESS" else None,
                "allocation": allocation_result.data if allocation_result.status.value == "SUCCESS" else None,
                "timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Error analyzing portfolio: {e}")
            return {"error": str(e)}
    
    async def check_rebalancing_needs(self) -> Dict[str, Any]:
        """Check if portfolio needs rebalancing"""
        try:
            rebalancing_result = await self.execute_tool(
                "portfolio_analysis_tool",
                analysis_type="rebalancing",
                account_name="default"
            )
            
            if rebalancing_result.status.value == "SUCCESS":
                rebalancing_data = rebalancing_result.data
                
                # If rebalancing is needed, create recommendations
                if rebalancing_data.get("needs_rebalancing", False):
                    recommendations = await self.create_rebalancing_recommendations(rebalancing_data)
                    rebalancing_data["recommendations"] = recommendations
                
                return rebalancing_data
            else:
                return {"error": rebalancing_result.error}
                
        except Exception as e:
            logger.error(f"Error checking rebalancing needs: {e}")
            return {"error": str(e)}
    
    async def create_rebalancing_recommendations(self, rebalancing_data: Dict) -> List[Dict]:
        """Create specific rebalancing recommendations"""
        recommendations = []
        
        try:
            rebalancing_actions = rebalancing_data.get("rebalancing_actions", [])
            
            for action in rebalancing_actions:
                if action["type"] == "REDUCE_POSITION":
                    recommendations.append({
                        "action": "SELL",
                        "symbol": action["symbol"],
                        "reason": f"Reduce overweight position from {action['current']:.1f}% to {action['target']:.1f}%",
                        "priority": "MEDIUM",
                        "percentage_to_reduce": action["current"] - action["target"]
                    })
                elif action["type"] == "DEPLOY_CASH":
                    recommendations.append({
                        "action": "BUY",
                        "reason": f"Deploy excess cash ({action['current']:.1f}% vs target {action['target']:.1f}%)",
                        "priority": "LOW",
                        "cash_to_deploy": action["current"] - action["target"]
                    })
            
            return recommendations
            
        except Exception as e:
            logger.error(f"Error creating rebalancing recommendations: {e}")
            return []
    
    async def update_investment_plans(self, portfolio_analysis: Dict) -> Dict[str, Any]:
        """Update investment plans based on current performance"""
        try:
            # Get current plans
            plans_result = await self.execute_tool(
                "plan_manager_tool",
                action="get_plans",
                agent_name=self.name
            )
            
            if plans_result.status.value != "SUCCESS":
                return {"error": plans_result.error}
            
            plans = plans_result.data
            updates_made = []
            
            # Update each active plan
            for plan in plans:
                if plan["status"] == "ACTIVE":
                    # Calculate current performance
                    performance = portfolio_analysis.get("performance", {})
                    current_return = performance.get("total_return_percentage", 0)
                    
                    # Create plan update
                    update_data = {
                        "update_type": "PERFORMANCE_REVIEW",
                        "summary": f"Portfolio performance review: {current_return:+.2f}% return",
                        "details": f"Current portfolio value: ${performance.get('current_value', 0):,.2f}\nTotal return: {current_return:+.2f}%\nCash position: {performance.get('cash_percentage', 0):.1f}%",
                        "portfolio_value": performance.get("current_value", 0),
                        "return_since_start": current_return,
                    }
                    
                    update_result = await self.execute_tool(
                        "plan_manager_tool",
                        action="add_plan_update",
                        plan_id=plan["id"],
                        update_data=update_data,
                        agent_name=self.name
                    )
                    
                    if update_result.status.value == "SUCCESS":
                        updates_made.append({
                            "plan_id": plan["id"],
                            "plan_name": plan["name"],
                            "update_id": update_result.data["update_id"]
                        })
            
            return {
                "plans_updated": len(updates_made),
                "updates": updates_made,
                "timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Error updating investment plans: {e}")
            return {"error": str(e)}
    
    async def generate_performance_report(self, portfolio_analysis: Dict) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        try:
            performance = portfolio_analysis.get("performance", {})
            allocation = portfolio_analysis.get("allocation", {})
            
            # Create performance summary
            report = {
                "report_date": datetime.now().isoformat(),
                "portfolio_value": performance.get("current_value", 0),
                "total_return": performance.get("total_return", 0),
                "total_return_percentage": performance.get("total_return_percentage", 0),
                "cash_percentage": allocation.get("cash_allocation", 0),
                "number_of_positions": allocation.get("number_of_positions", 0),
                "largest_position": allocation.get("largest_position", 0),
                "allocation_score": allocation.get("allocation_score", 0),
                "top_performers": self._get_top_performers(allocation),
                "recommendations": self._generate_portfolio_recommendations(portfolio_analysis),
            }
            
            # Send performance notification
            await self.execute_tool(
                "notification_tool",
                notification_type="performance_update",
                title=f"Portfolio Performance Report",
                message=f"Portfolio value: ${report['portfolio_value']:,.2f} ({report['total_return_percentage']:+.2f}%)",
                data=report,
                priority="NORMAL"
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return {"error": str(e)}
    
    def _get_top_performers(self, allocation: Dict) -> List[Dict]:
        """Get top performing positions"""
        position_allocation = allocation.get("position_allocation", [])
        
        # Sort by unrealized P&L percentage (if available)
        top_performers = sorted(
            [p for p in position_allocation if p.get("unrealized_pnl", 0) > 0],
            key=lambda x: x.get("unrealized_pnl", 0),
            reverse=True
        )[:3]
        
        return top_performers
    
    def _generate_portfolio_recommendations(self, portfolio_analysis: Dict) -> List[str]:
        """Generate portfolio-level recommendations"""
        recommendations = []
        
        allocation = portfolio_analysis.get("allocation", {})
        performance = portfolio_analysis.get("performance", {})
        
        # Cash allocation recommendations
        cash_pct = allocation.get("cash_allocation", 0)
        if cash_pct > 20:
            recommendations.append("Consider deploying excess cash into investments")
        elif cash_pct < 5:
            recommendations.append("Consider increasing cash reserves for opportunities")
        
        # Diversification recommendations
        num_positions = allocation.get("number_of_positions", 0)
        if num_positions < 5:
            recommendations.append("Consider adding more positions for better diversification")
        elif num_positions > 20:
            recommendations.append("Consider consolidating positions to reduce complexity")
        
        # Performance recommendations
        total_return_pct = performance.get("total_return_percentage", 0)
        if total_return_pct < -10:
            recommendations.append("Review underperforming positions and consider risk management")
        elif total_return_pct > 20:
            recommendations.append("Consider taking profits on strong performers")
        
        return recommendations
