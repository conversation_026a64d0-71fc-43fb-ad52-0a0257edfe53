"""
Market Analyst Agent - Analyzes market trends and economic indicators
"""

import json
from datetime import datetime
from typing import Dict, List, Any

from loguru import logger

from .base import BaseAgent, AgentConfig
from ..models.agent import AgentType
from ..tools import (
    MarketDataTool, TechnicalAnalysisTool, AlertTool, NotificationTool,
    NewsAnalysisTool, EconomicIndicatorsTool
)
from ..config import settings


MARKET_ANALYST_SYSTEM_PROMPT = """You are a Market Analyst AI agent specializing in comprehensive Chinese A-share stock market analysis and economic research.

## Your Role and Responsibilities

As the Market Analyst for Chinese A-share markets, you are responsible for:

1. **Market Trend Analysis**: Monitor and analyze overall A-share market trends, identifying bullish, bearish, or sideways patterns
2. **Economic Indicator Monitoring**: Track key Chinese economic indicators (GDP, inflation, employment, interest rates) and assess their market impact
3. **Market Sentiment Analysis**: Gauge A-share market sentiment through various indicators and news analysis
4. **Sector Analysis**: Analyze performance and trends across different A-share market sectors
5. **Risk Assessment**: Identify potential market risks and opportunities specific to Chinese markets
6. **Market Context**: Provide market context for other agents' decision-making processes in A-share trading

## Your Analytical Framework

### Market Trend Analysis
- Analyze major Chinese market indices (Shanghai Composite, Shenzhen Component, CSI 300, ChiNext)
- Identify support and resistance levels for A-share indices
- Assess market volatility and volume patterns in Chinese markets
- Determine overall A-share market direction and strength

### Economic Indicators
- Monitor People's Bank of China (PBOC) policy and interest rate changes
- Track Chinese inflation data (CPI, PPI) and employment statistics
- Analyze China's GDP growth, consumer confidence, and business sentiment
- Assess global economic factors affecting Chinese A-share markets

### Market Sentiment
- Analyze fear and greed indicators
- Monitor options flow and put/call ratios
- Assess insider trading activity and institutional flows
- Track market breadth and advance/decline ratios

### Sector Rotation
- Identify which sectors are leading or lagging
- Analyze sector rotation patterns
- Assess sector-specific catalysts and headwinds
- Monitor sector valuations and relative performance

## Your Communication Style

- Provide clear, data-driven analysis with specific metrics
- Use confidence levels (High/Medium/Low) for your assessments
- Include both bullish and bearish scenarios in your analysis
- Highlight key risks and opportunities
- Support conclusions with specific data points and reasoning

## Your Tools and Capabilities

You have access to:
- Market data retrieval and analysis tools
- Alert generation for significant market events
- Notification systems for sharing insights
- Historical data analysis capabilities

## Decision-Making Process

1. **Data Collection**: Gather relevant market data and economic indicators
2. **Analysis**: Apply technical and fundamental analysis techniques
3. **Synthesis**: Combine multiple data points into coherent market view
4. **Risk Assessment**: Identify potential risks and their probability
5. **Communication**: Share insights with other agents and stakeholders

## Key Performance Indicators

Your effectiveness is measured by:
- Accuracy of market trend predictions
- Timeliness of risk identification
- Quality of sector analysis and recommendations
- Usefulness of market context provided to other agents

## Interaction with Other Agents

- **Stock Selector**: Provide market context and sector preferences
- **Trading Decision Maker**: Share market timing insights and risk factors
- **Portfolio Manager**: Offer market outlook for asset allocation decisions
- **Risk Controller**: Alert to emerging market risks and volatility

## Important Guidelines

1. **Objectivity**: Base analysis on data, not emotions or biases
2. **Timeliness**: Provide timely updates on changing market conditions
3. **Clarity**: Communicate complex analysis in understandable terms
4. **Risk Awareness**: Always consider and communicate potential risks
5. **Continuous Learning**: Adapt analysis based on market feedback and outcomes

Remember: Your analysis forms the foundation for other agents' decisions. Accuracy, timeliness, and clarity are paramount to the success of the entire trading system.

When market conditions change significantly, immediately generate alerts and update your analysis. Your insights help the entire system navigate market volatility and capitalize on opportunities.
"""


class MarketAnalystAgent(BaseAgent):
    """Market Analyst Agent implementation"""
    
    def __init__(self):
        # Initialize tools
        tools = [
            MarketDataTool(),
            TechnicalAnalysisTool(),
            NewsAnalysisTool(),
            EconomicIndicatorsTool(),
            AlertTool(),
            NotificationTool(),
        ]
        
        config = AgentConfig(
            name="market_analyst",
            agent_type=AgentType.MARKET_ANALYST,
            description="Analyzes market trends, economic indicators, and provides market context",
            system_prompt=MARKET_ANALYST_SYSTEM_PROMPT,
            model_name=settings.openai.model,
            temperature=0.3,  # Lower temperature for more consistent analysis
            max_tokens=2500,
            tools=tools,
            update_interval=300,  # 5 minutes
        )
        
        super().__init__(config)
        
        # Agent-specific state
        self.last_market_analysis = None
        self.market_alerts_sent = []
        self.analysis_history = []
    
    async def execute_cycle(self) -> Dict[str, Any]:
        """Execute one cycle of market analysis"""
        try:
            logger.info(f"Market Analyst {self.name} starting analysis cycle")
            
            # Step 1: Get current market status
            market_status = await self.get_market_status()
            
            # Step 2: Analyze market trends
            market_analysis = await self.analyze_market_trends()
            
            # Step 3: Check for significant changes or alerts
            alerts_generated = await self.check_for_alerts(market_analysis)
            
            # Step 4: Update analysis history
            self.update_analysis_history(market_analysis)
            
            # Step 5: Prepare summary for other agents
            summary = await self.prepare_analysis_summary(market_analysis)
            
            result = {
                "cycle_completed": True,
                "market_status": market_status,
                "analysis": market_analysis,
                "alerts_generated": alerts_generated,
                "summary": summary,
                "timestamp": datetime.now().isoformat(),
            }
            
            logger.info(f"Market Analyst {self.name} completed analysis cycle")
            return result
            
        except Exception as e:
            logger.error(f"Market Analyst {self.name} cycle failed: {e}")
            raise
    
    async def get_market_status(self) -> Dict[str, Any]:
        """Get current market status"""
        try:
            # Get market status
            status_result = await self.execute_tool(
                "market_data_tool",
                action="get_market_status"
            )
            
            if status_result.status.value == "SUCCESS":
                return status_result.data
            else:
                logger.warning(f"Failed to get market status: {status_result.error}")
                return {"status": "UNKNOWN", "error": status_result.error}
                
        except Exception as e:
            logger.error(f"Error getting market status: {e}")
            return {"status": "ERROR", "error": str(e)}
    
    async def analyze_market_trends(self) -> Dict[str, Any]:
        """Perform comprehensive market analysis"""
        try:
            # Analyze major indices
            indices = ["SPY", "QQQ", "DIA"]  # S&P 500, NASDAQ, Dow Jones proxies
            index_analysis = {}
            
            for index in indices:
                # Get current price and historical data
                price_result = await self.execute_tool(
                    "market_data_tool",
                    action="get_current_price",
                    symbol=index
                )
                
                historical_result = await self.execute_tool(
                    "market_data_tool",
                    action="get_historical_data",
                    symbol=index,
                    period="1mo",
                    interval="1d"
                )
                
                # Perform technical analysis
                technical_result = await self.execute_tool(
                    "technical_analysis_tool",
                    symbol=index,
                    indicators=["sma_20", "sma_50", "rsi"],
                    period="3mo"
                )
                
                index_analysis[index] = {
                    "current_price": price_result.data if price_result.status.value == "SUCCESS" else None,
                    "historical_data": historical_result.data if historical_result.status.value == "SUCCESS" else None,
                    "technical_analysis": technical_result.data if technical_result.status.value == "SUCCESS" else None,
                }
            
            # Use LLM to analyze the data
            analysis_prompt = self._create_analysis_prompt(index_analysis)
            
            messages = [
                {
                    "role": "user",
                    "content": analysis_prompt
                }
            ]
            
            llm_response = await self.call_llm(messages)
            
            # Parse LLM analysis
            analysis = {
                "indices_analysis": index_analysis,
                "llm_analysis": llm_response["content"],
                "market_trend": self._extract_market_trend(llm_response["content"]),
                "confidence_level": self._extract_confidence_level(llm_response["content"]),
                "key_risks": self._extract_key_risks(llm_response["content"]),
                "opportunities": self._extract_opportunities(llm_response["content"]),
                "timestamp": datetime.now().isoformat(),
            }
            
            self.last_market_analysis = analysis
            return analysis
            
        except Exception as e:
            logger.error(f"Error in market analysis: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}
    
    def _create_analysis_prompt(self, index_data: Dict) -> str:
        """Create prompt for LLM analysis"""
        prompt = """Please analyze the current market conditions based on the following data:

"""
        
        for index, data in index_data.items():
            prompt += f"\n## {index} Analysis\n"
            
            if data["current_price"]:
                current_price = data["current_price"].get("current_price", "N/A")
                prompt += f"Current Price: ${current_price}\n"
            
            if data["technical_analysis"]:
                tech_data = data["technical_analysis"]
                prompt += f"Technical Indicators:\n"
                if "sma_20" in tech_data.get("indicators", {}):
                    prompt += f"- 20-day SMA: ${tech_data['indicators']['sma_20']}\n"
                if "sma_50" in tech_data.get("indicators", {}):
                    prompt += f"- 50-day SMA: ${tech_data['indicators']['sma_50']}\n"
                if "rsi" in tech_data.get("indicators", {}):
                    prompt += f"- RSI: {tech_data['indicators']['rsi']}\n"
                if "trend" in tech_data:
                    prompt += f"- Trend: {tech_data['trend']}\n"
        
        prompt += """

Based on this data, please provide:

1. **Overall Market Trend**: BULLISH/BEARISH/NEUTRAL with reasoning
2. **Confidence Level**: HIGH/MEDIUM/LOW for your assessment
3. **Key Risks**: List 2-3 main risks to watch
4. **Opportunities**: List 2-3 potential opportunities
5. **Market Outlook**: Short-term (1-2 weeks) and medium-term (1-3 months) outlook
6. **Sector Preferences**: Which sectors might outperform/underperform

Please be specific and data-driven in your analysis.
"""
        
        return prompt
    
    def _extract_market_trend(self, analysis: str) -> str:
        """Extract market trend from LLM analysis"""
        analysis_lower = analysis.lower()
        if "bullish" in analysis_lower:
            return "BULLISH"
        elif "bearish" in analysis_lower:
            return "BEARISH"
        else:
            return "NEUTRAL"
    
    def _extract_confidence_level(self, analysis: str) -> str:
        """Extract confidence level from LLM analysis"""
        analysis_lower = analysis.lower()
        if "high confidence" in analysis_lower or "confidence level: high" in analysis_lower:
            return "HIGH"
        elif "low confidence" in analysis_lower or "confidence level: low" in analysis_lower:
            return "LOW"
        else:
            return "MEDIUM"
    
    def _extract_key_risks(self, analysis: str) -> List[str]:
        """Extract key risks from LLM analysis"""
        # Simple extraction - in production, would use more sophisticated NLP
        risks = []
        lines = analysis.split('\n')
        in_risks_section = False
        
        for line in lines:
            if "key risks" in line.lower() or "risks:" in line.lower():
                in_risks_section = True
                continue
            elif in_risks_section and line.strip():
                if line.strip().startswith(('-', '•', '1.', '2.', '3.')):
                    risks.append(line.strip().lstrip('-•123. '))
                elif not line.strip().startswith(('-', '•', '1.', '2.', '3.')) and len(risks) > 0:
                    break
        
        return risks[:3]  # Limit to top 3 risks
    
    def _extract_opportunities(self, analysis: str) -> List[str]:
        """Extract opportunities from LLM analysis"""
        # Simple extraction - in production, would use more sophisticated NLP
        opportunities = []
        lines = analysis.split('\n')
        in_opportunities_section = False
        
        for line in lines:
            if "opportunities" in line.lower():
                in_opportunities_section = True
                continue
            elif in_opportunities_section and line.strip():
                if line.strip().startswith(('-', '•', '1.', '2.', '3.')):
                    opportunities.append(line.strip().lstrip('-•123. '))
                elif not line.strip().startswith(('-', '•', '1.', '2.', '3.')) and len(opportunities) > 0:
                    break
        
        return opportunities[:3]  # Limit to top 3 opportunities
    
    async def check_for_alerts(self, analysis: Dict) -> List[Dict]:
        """Check if any alerts should be generated"""
        alerts_generated = []
        
        try:
            # Check for significant market changes
            if self.last_market_analysis:
                prev_trend = self.last_market_analysis.get("market_trend", "NEUTRAL")
                current_trend = analysis.get("market_trend", "NEUTRAL")
                
                if prev_trend != current_trend:
                    # Market trend changed
                    alert_result = await self.execute_tool(
                        "alert_tool",
                        alert_type="market_alert",
                        level="WARNING",
                        title=f"Market Trend Change: {prev_trend} → {current_trend}",
                        message=f"Market trend has changed from {prev_trend} to {current_trend}",
                        data={
                            "previous_trend": prev_trend,
                            "current_trend": current_trend,
                            "confidence": analysis.get("confidence_level", "MEDIUM")
                        },
                        agent_name=self.name
                    )
                    
                    if alert_result.status.value == "SUCCESS":
                        alerts_generated.append(alert_result.data)
            
            # Check for high-risk conditions
            key_risks = analysis.get("key_risks", [])
            if len(key_risks) >= 3:
                alert_result = await self.execute_tool(
                    "alert_tool",
                    alert_type="risk_alert",
                    level="WARNING",
                    title="Multiple Market Risks Identified",
                    message=f"Identified {len(key_risks)} key market risks",
                    data={"risks": key_risks},
                    agent_name=self.name
                )
                
                if alert_result.status.value == "SUCCESS":
                    alerts_generated.append(alert_result.data)
            
            return alerts_generated
            
        except Exception as e:
            logger.error(f"Error checking for alerts: {e}")
            return alerts_generated
    
    def update_analysis_history(self, analysis: Dict) -> None:
        """Update analysis history"""
        self.analysis_history.append({
            "timestamp": datetime.now().isoformat(),
            "analysis": analysis
        })
        
        # Keep only last 24 hours of analysis (assuming 5-minute intervals)
        if len(self.analysis_history) > 288:  # 24 * 60 / 5 = 288
            self.analysis_history = self.analysis_history[-200:]
    
    async def prepare_analysis_summary(self, analysis: Dict) -> Dict[str, Any]:
        """Prepare analysis summary for other agents"""
        summary = {
            "agent": self.name,
            "timestamp": datetime.now().isoformat(),
            "market_trend": analysis.get("market_trend", "NEUTRAL"),
            "confidence_level": analysis.get("confidence_level", "MEDIUM"),
            "key_risks": analysis.get("key_risks", []),
            "opportunities": analysis.get("opportunities", []),
            "market_status": "OPEN",  # Would be determined from market data
            "recommendation": self._generate_recommendation(analysis),
        }
        
        # Send notification to other agents
        try:
            await self.execute_tool(
                "notification_tool",
                notification_type="market_update",
                title=f"Market Analysis Update - {analysis.get('market_trend', 'NEUTRAL')}",
                message=f"Market trend: {analysis.get('market_trend', 'NEUTRAL')} (Confidence: {analysis.get('confidence_level', 'MEDIUM')})",
                data=summary,
                priority="NORMAL"
            )
        except Exception as e:
            logger.error(f"Failed to send market update notification: {e}")
        
        return summary
    
    def _generate_recommendation(self, analysis: Dict) -> str:
        """Generate recommendation based on analysis"""
        trend = analysis.get("market_trend", "NEUTRAL")
        confidence = analysis.get("confidence_level", "MEDIUM")
        risks = len(analysis.get("key_risks", []))
        
        if trend == "BULLISH" and confidence == "HIGH" and risks <= 1:
            return "FAVORABLE_FOR_BUYING"
        elif trend == "BEARISH" and confidence == "HIGH":
            return "CONSIDER_DEFENSIVE_POSITIONS"
        elif risks >= 3:
            return "EXERCISE_CAUTION"
        else:
            return "NEUTRAL_STANCE"
