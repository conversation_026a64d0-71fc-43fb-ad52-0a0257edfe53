"""
Trading Decision Maker Agent - Makes specific buy/sell trading decisions
"""

from datetime import datetime
from typing import Dict, List, Any

from loguru import logger

from .base import BaseAgent, AgentConfig
from ..models.agent import AgentType
from ..tools import DecisionRecorderTool, TransactionTool, AccountTool, PositionTool, MarketDataTool, AlertTool
from ..config import settings


TRADING_DECISION_MAKER_SYSTEM_PROMPT = """You are a Trading Decision Maker AI agent responsible for executing specific trading decisions based on analysis from other agents.

## Your Role and Responsibilities

As the Trading Decision Maker, you are responsible for:

1. **Trade Execution**: Make specific buy/sell/hold decisions based on analysis
2. **Position Sizing**: Determine appropriate position sizes based on risk parameters
3. **Entry/Exit Timing**: Identify optimal entry and exit points for trades
4. **Risk Management**: Set stop-loss and take-profit levels for all positions
5. **Decision Documentation**: Record detailed reasoning for every trading decision
6. **Order Management**: Manage trade execution and monitor order status

## Your Decision-Making Framework

### Trade Analysis
- Evaluate recommendations from Stock Selector and Market Analyst
- Assess current market conditions and timing
- Consider portfolio context and existing positions
- Analyze risk-reward ratios for potential trades

### Position Sizing
- Use portfolio percentage limits (max 10% per position by default)
- Consider volatility and risk level of the stock
- Account for correlation with existing positions
- Maintain appropriate cash reserves

### Risk Management
- Set stop-loss levels (typically 5-8% below entry)
- Define take-profit targets (typically 15-25% above entry)
- Monitor position sizes relative to portfolio
- Implement trailing stops for profitable positions

### Decision Documentation
- Record specific entry/exit prices and reasoning
- Document market conditions at time of decision
- Note risk factors and mitigation strategies
- Track decision outcomes for continuous improvement

## Your Trading Rules

1. **Never exceed maximum position size limits**
2. **Always set stop-loss and take-profit levels**
3. **Document reasoning for every trade decision**
4. **Consider market conditions and timing**
5. **Maintain diversification across sectors**
6. **Preserve capital during uncertain market conditions**

## Communication Style

- Provide clear, actionable trading decisions
- Include specific prices, quantities, and timeframes
- Explain reasoning with supporting data
- Highlight key risks and exit strategies
- Use confidence levels for decision quality

Remember: Every trading decision impacts portfolio performance. Focus on high-probability trades with favorable risk-reward ratios and always preserve capital.
"""


class TradingDecisionMakerAgent(BaseAgent):
    """Trading Decision Maker Agent implementation"""
    
    def __init__(self):
        tools = [
            DecisionRecorderTool(),
            TransactionTool(),
            AccountTool(),
            PositionTool(),
            MarketDataTool(),
            AlertTool(),
        ]
        
        config = AgentConfig(
            name="trading_decision_maker",
            agent_type=AgentType.TRADING_DECISION_MAKER,
            description="Makes specific buy/sell trading decisions with risk management",
            system_prompt=TRADING_DECISION_MAKER_SYSTEM_PROMPT,
            model_name=settings.openai.model,
            temperature=0.2,  # Lower temperature for more consistent decisions
            max_tokens=2000,
            tools=tools,
            update_interval=300,  # 5 minutes
        )
        
        super().__init__(config)
        self.pending_decisions = []
        self.recent_decisions = []
    
    async def execute_cycle(self) -> Dict[str, Any]:
        """Execute trading decision cycle"""
        try:
            logger.info(f"Trading Decision Maker {self.name} starting decision cycle")
            
            # Step 1: Get account status
            account_status = await self.get_account_status()
            
            # Step 2: Review pending decisions
            pending_review = await self.review_pending_decisions()
            
            # Step 3: Evaluate new opportunities
            new_decisions = await self.evaluate_opportunities(account_status)
            
            # Step 4: Execute approved decisions
            executions = await self.execute_decisions()
            
            result = {
                "cycle_completed": True,
                "account_status": account_status,
                "pending_review": pending_review,
                "new_decisions": new_decisions,
                "executions": executions,
                "timestamp": datetime.now().isoformat(),
            }
            
            logger.info(f"Trading Decision Maker {self.name} completed decision cycle")
            return result
            
        except Exception as e:
            logger.error(f"Trading Decision Maker {self.name} cycle failed: {e}")
            raise
    
    async def get_account_status(self) -> Dict[str, Any]:
        """Get current account status"""
        try:
            # Get account balance
            balance_result = await self.execute_tool(
                "account_tool",
                action="get_balance",
                account_name="default"
            )
            
            # Get current positions
            positions_result = await self.execute_tool(
                "position_tool",
                action="get_positions",
                account_name="default"
            )
            
            return {
                "balance": balance_result.data if balance_result.status.value == "SUCCESS" else None,
                "positions": positions_result.data if positions_result.status.value == "SUCCESS" else [],
                "timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Error getting account status: {e}")
            return {"error": str(e)}
    
    async def review_pending_decisions(self) -> Dict[str, Any]:
        """Review pending trading decisions"""
        try:
            # Get recent decisions
            decisions_result = await self.execute_tool(
                "decision_recorder_tool",
                action="get_decisions",
                agent_name=self.name,
                limit=10
            )
            
            if decisions_result.status.value != "SUCCESS":
                return {"error": decisions_result.error}
            
            decisions = decisions_result.data
            pending_decisions = [d for d in decisions if d["status"] == "PENDING"]
            
            # Review each pending decision
            reviews = []
            for decision in pending_decisions:
                review = await self.review_decision(decision)
                reviews.append(review)
            
            return {
                "pending_count": len(pending_decisions),
                "reviews": reviews,
                "timestamp": datetime.now().isoformat(),
            }
            
        except Exception as e:
            logger.error(f"Error reviewing pending decisions: {e}")
            return {"error": str(e)}
    
    async def review_decision(self, decision: Dict) -> Dict[str, Any]:
        """Review a specific pending decision"""
        try:
            symbol = decision["stock_symbol"]
            
            # Get current market price
            price_result = await self.execute_tool(
                "market_data_tool",
                action="get_current_price",
                symbol=symbol
            )
            
            current_price = None
            if price_result.status.value == "SUCCESS":
                current_price = price_result.data.get("current_price")
            
            # Analyze if decision should be executed
            target_price = decision.get("target_price")
            decision_type = decision["decision_type"]
            
            should_execute = False
            reason = ""
            
            if current_price and target_price:
                if decision_type == "BUY" and current_price <= target_price * 1.02:  # 2% tolerance
                    should_execute = True
                    reason = f"Current price ${current_price} is at/below target ${target_price}"
                elif decision_type == "SELL" and current_price >= target_price * 0.98:  # 2% tolerance
                    should_execute = True
                    reason = f"Current price ${current_price} is at/above target ${target_price}"
                else:
                    reason = f"Current price ${current_price} not at target ${target_price}"
            
            # Execute decision if conditions are met
            if should_execute:
                await self.execute_tool(
                    "decision_recorder_tool",
                    action="approve_decision",
                    decision_id=decision["id"]
                )
            
            return {
                "decision_id": decision["id"],
                "symbol": symbol,
                "decision_type": decision_type,
                "current_price": current_price,
                "target_price": target_price,
                "should_execute": should_execute,
                "reason": reason,
            }
            
        except Exception as e:
            logger.error(f"Error reviewing decision {decision.get('id')}: {e}")
            return {"error": str(e)}
    
    async def evaluate_opportunities(self, account_status: Dict) -> List[Dict]:
        """Evaluate new trading opportunities"""
        new_decisions = []
        
        try:
            # This would typically get recommendations from Stock Selector
            # For now, we'll simulate with a simple example
            
            # Check if we have available cash for new positions
            balance = account_status.get("balance", {})
            current_cash = balance.get("current_cash", 0)
            total_value = balance.get("total_value", 0)
            
            if current_cash < total_value * 0.1:  # Less than 10% cash
                logger.info("Insufficient cash for new positions")
                return new_decisions
            
            # Example: Create a buy decision for a recommended stock
            # In production, this would come from Stock Selector agent
            example_opportunity = {
                "symbol": "AAPL",
                "recommendation": "BUY",
                "confidence_score": 7,
                "reasoning": "Strong technical setup with market support"
            }
            
            # Calculate position size (max 10% of portfolio)
            max_position_value = total_value * 0.10
            
            # Get current price
            price_result = await self.execute_tool(
                "market_data_tool",
                action="get_current_price",
                symbol=example_opportunity["symbol"]
            )
            
            if price_result.status.value == "SUCCESS":
                current_price = price_result.data.get("current_price", 0)
                
                if current_price > 0:
                    # Calculate quantity
                    quantity = int(max_position_value / current_price)
                    
                    if quantity > 0:
                        # Create decision
                        decision_data = {
                            "decision_type": "BUY",
                            "stock_symbol": example_opportunity["symbol"],
                            "quantity": quantity,
                            "target_price": current_price,
                            "stop_loss_price": current_price * 0.95,  # 5% stop loss
                            "take_profit_price": current_price * 1.15,  # 15% take profit
                            "reasoning": example_opportunity["reasoning"],
                            "confidence_score": example_opportunity["confidence_score"] / 10.0,
                            "market_price_at_decision": current_price,
                        }
                        
                        # Record decision
                        record_result = await self.execute_tool(
                            "decision_recorder_tool",
                            action="record_decision",
                            decision_data=decision_data,
                            agent_name=self.name
                        )
                        
                        if record_result.status.value == "SUCCESS":
                            new_decisions.append(record_result.data)
            
            return new_decisions
            
        except Exception as e:
            logger.error(f"Error evaluating opportunities: {e}")
            return new_decisions
    
    async def execute_decisions(self) -> List[Dict]:
        """Execute approved trading decisions"""
        executions = []
        
        try:
            # Get approved decisions
            decisions_result = await self.execute_tool(
                "decision_recorder_tool",
                action="get_decisions",
                agent_name=self.name,
                limit=5
            )
            
            if decisions_result.status.value != "SUCCESS":
                return executions
            
            approved_decisions = [
                d for d in decisions_result.data 
                if d["status"] == "APPROVED"
            ]
            
            for decision in approved_decisions:
                try:
                    # Create transaction
                    transaction_data = {
                        "transaction_type": decision["decision_type"],
                        "stock_symbol": decision["stock_symbol"],
                        "quantity": decision["quantity"],
                        "price": decision["target_price"],
                        "amount": decision["quantity"] * decision["target_price"],
                        "notes": f"Executed from decision {decision['id']}"
                    }
                    
                    # Record transaction
                    transaction_result = await self.execute_tool(
                        "transaction_tool",
                        action="record_transaction",
                        transaction_data=transaction_data
                    )
                    
                    if transaction_result.status.value == "SUCCESS":
                        # Execute transaction
                        execute_result = await self.execute_tool(
                            "transaction_tool",
                            action="execute_transaction",
                            transaction_id=transaction_result.data["transaction_id"]
                        )
                        
                        if execute_result.status.value == "SUCCESS":
                            # Update decision status
                            await self.execute_tool(
                                "decision_recorder_tool",
                                action="update_decision",
                                decision_id=decision["id"],
                                update_data={"status": "EXECUTED"}
                            )
                            
                            executions.append({
                                "decision_id": decision["id"],
                                "transaction_id": transaction_result.data["transaction_id"],
                                "symbol": decision["stock_symbol"],
                                "action": decision["decision_type"],
                                "quantity": decision["quantity"],
                                "price": decision["target_price"],
                                "status": "EXECUTED"
                            })
                            
                            # Generate alert for execution
                            await self.execute_tool(
                                "alert_tool",
                                alert_type="trade_execution",
                                level="INFO",
                                title=f"Trade Executed: {decision['decision_type']} {decision['stock_symbol']}",
                                message=f"Executed {decision['decision_type']} of {decision['quantity']} shares of {decision['stock_symbol']} at ${decision['target_price']}",
                                data={
                                    "symbol": decision["stock_symbol"],
                                    "action": decision["decision_type"],
                                    "quantity": decision["quantity"],
                                    "price": decision["target_price"]
                                },
                                agent_name=self.name
                            )
                
                except Exception as e:
                    logger.error(f"Error executing decision {decision['id']}: {e}")
                    executions.append({
                        "decision_id": decision["id"],
                        "error": str(e),
                        "status": "FAILED"
                    })
            
            return executions
            
        except Exception as e:
            logger.error(f"Error executing decisions: {e}")
            return executions
