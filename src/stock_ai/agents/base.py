"""
Base agent class and configuration for Stock AI Agents system
"""

import asyncio
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field

import openai
from loguru import logger

from ..config import settings
from ..tools.base import BaseTool, ToolResult
from ..models.agent import AgentType, AgentStatus


@dataclass
class AgentConfig:
    """Agent configuration"""
    name: str
    agent_type: AgentType
    description: str
    system_prompt: str
    model_name: str = "gpt-4"
    temperature: float = 0.7
    max_tokens: int = 2000
    tools: List[BaseTool] = field(default_factory=list)
    update_interval: int = 60  # seconds
    enabled: bool = True


class BaseAgent(ABC):
    """Base class for all AI agents"""
    
    def __init__(self, config: AgentConfig):
        self.config = config
        self.name = config.name
        self.agent_type = config.agent_type
        self.status = AgentStatus.INACTIVE
        self.tools = {tool.name: tool for tool in config.tools}
        self.conversation_history: List[Dict[str, Any]] = []
        self.last_execution: Optional[datetime] = None
        self.execution_count = 0
        self.error_count = 0
        self.last_error: Optional[str] = None
        
        # Initialize OpenAI client
        self.client = openai.AsyncOpenAI(
            api_key=settings.openai.api_key,
            base_url=settings.openai.base_url
        )
    
    async def start(self) -> None:
        """Start the agent"""
        self.status = AgentStatus.ACTIVE
        logger.info(f"Agent {self.name} started")
    
    async def stop(self) -> None:
        """Stop the agent"""
        self.status = AgentStatus.INACTIVE
        logger.info(f"Agent {self.name} stopped")
    
    async def pause(self) -> None:
        """Pause the agent"""
        self.status = AgentStatus.PAUSED
        logger.info(f"Agent {self.name} paused")
    
    async def resume(self) -> None:
        """Resume the agent"""
        self.status = AgentStatus.ACTIVE
        logger.info(f"Agent {self.name} resumed")
    
    @abstractmethod
    async def execute_cycle(self) -> Dict[str, Any]:
        """Execute one cycle of agent logic"""
        pass
    
    async def run_continuous(self) -> None:
        """Run agent continuously with update intervals"""
        logger.info(f"Starting continuous execution for agent {self.name}")
        
        while self.status in [AgentStatus.ACTIVE, AgentStatus.PAUSED]:
            try:
                if self.status == AgentStatus.ACTIVE:
                    # Execute agent cycle
                    result = await self.execute_cycle()
                    self.execution_count += 1
                    self.last_execution = datetime.now()
                    
                    logger.debug(f"Agent {self.name} completed cycle {self.execution_count}")
                
                # Wait for next cycle
                await asyncio.sleep(self.config.update_interval)
                
            except Exception as e:
                self.error_count += 1
                self.last_error = str(e)
                self.status = AgentStatus.ERROR
                logger.error(f"Agent {self.name} encountered error: {e}")
                
                # Wait before retrying
                await asyncio.sleep(self.config.update_interval * 2)
                
                # Try to recover
                if self.error_count < 5:
                    self.status = AgentStatus.ACTIVE
                    logger.info(f"Agent {self.name} attempting recovery (attempt {self.error_count})")
                else:
                    logger.error(f"Agent {self.name} failed too many times, stopping")
                    break
    
    async def call_llm(self, messages: List[Dict[str, str]], tools: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Call the language model"""
        try:
            # Prepare messages with system prompt
            full_messages = [
                {"role": "system", "content": self.config.system_prompt}
            ] + messages
            
            # Prepare tools for function calling
            llm_tools = []
            if tools:
                llm_tools = tools
            elif self.tools:
                llm_tools = [
                    {
                        "type": "function",
                        "function": tool.definition.to_mcp_schema()
                    }
                    for tool in self.tools.values()
                ]
            
            # Call OpenAI API
            kwargs = {
                "model": self.config.model_name,
                "messages": full_messages,
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
            }
            
            if llm_tools:
                kwargs["tools"] = llm_tools
                kwargs["tool_choice"] = "auto"
            
            response = await self.client.chat.completions.create(**kwargs)
            
            # Process response
            message = response.choices[0].message
            result = {
                "content": message.content,
                "tool_calls": [],
                "usage": {
                    "prompt_tokens": response.usage.prompt_tokens,
                    "completion_tokens": response.usage.completion_tokens,
                    "total_tokens": response.usage.total_tokens,
                }
            }
            
            # Process tool calls
            if message.tool_calls:
                for tool_call in message.tool_calls:
                    result["tool_calls"].append({
                        "id": tool_call.id,
                        "function": tool_call.function.name,
                        "arguments": tool_call.function.arguments,
                    })
            
            return result
            
        except Exception as e:
            logger.error(f"LLM call failed for agent {self.name}: {e}")
            raise
    
    async def execute_tool(self, tool_name: str, **kwargs) -> ToolResult:
        """Execute a tool"""
        if tool_name not in self.tools:
            raise ValueError(f"Tool '{tool_name}' not available for agent {self.name}")
        
        tool = self.tools[tool_name]
        return await tool.safe_execute(**kwargs)
    
    async def process_tool_calls(self, tool_calls: List[Dict]) -> List[Dict]:
        """Process multiple tool calls"""
        results = []
        
        for tool_call in tool_calls:
            try:
                # Parse arguments
                import json
                arguments = json.loads(tool_call["arguments"])
                
                # Execute tool
                result = await self.execute_tool(tool_call["function"], **arguments)
                
                results.append({
                    "tool_call_id": tool_call["id"],
                    "function_name": tool_call["function"],
                    "result": result.to_dict(),
                })
                
            except Exception as e:
                logger.error(f"Tool execution failed: {e}")
                results.append({
                    "tool_call_id": tool_call["id"],
                    "function_name": tool_call["function"],
                    "error": str(e),
                })
        
        return results
    
    def add_to_conversation(self, role: str, content: str, tool_calls: List = None, tool_results: List = None) -> None:
        """Add message to conversation history"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
        }
        
        if tool_calls:
            message["tool_calls"] = tool_calls
        
        if tool_results:
            message["tool_results"] = tool_results
        
        self.conversation_history.append(message)
        
        # Keep conversation history manageable
        if len(self.conversation_history) > 50:
            self.conversation_history = self.conversation_history[-40:]
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status information"""
        return {
            "name": self.name,
            "type": self.agent_type.value,
            "status": self.status.value,
            "execution_count": self.execution_count,
            "error_count": self.error_count,
            "last_execution": self.last_execution.isoformat() if self.last_execution else None,
            "last_error": self.last_error,
            "conversation_length": len(self.conversation_history),
            "available_tools": list(self.tools.keys()),
        }
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}', status={self.status.value})>"
