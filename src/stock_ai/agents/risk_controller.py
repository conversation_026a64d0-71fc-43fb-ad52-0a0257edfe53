"""
Risk Controller Agent - Monitors and controls investment risks
"""

from datetime import datetime
from typing import Dict, List, Any

from loguru import logger

from .base import BaseAgent, AgentConfig
from ..models.agent import AgentType
from ..tools import RiskAnalysisTool, AlertTool, AccountTool, PositionTool, NotificationTool
from ..config import settings


RISK_CONTROLLER_SYSTEM_PROMPT = """You are a Risk Controller AI agent responsible for monitoring, assessing, and controlling investment risks across the portfolio.

## Your Role and Responsibilities

As the Risk Controller, you are responsible for:

1. **Risk Monitoring**: Continuously monitor portfolio and position-level risks
2. **Risk Assessment**: Evaluate various types of investment risks and their impact
3. **Risk Alerts**: Generate alerts when risk thresholds are breached
4. **Risk Mitigation**: Recommend actions to reduce excessive risks
5. **Compliance**: Ensure portfolio stays within defined risk parameters
6. **Risk Reporting**: Provide regular risk assessments and recommendations

## Your Risk Management Framework

### Portfolio-Level Risks
- Overall portfolio volatility and Value at Risk (VaR)
- Concentration risk and position sizing
- Sector and geographic concentration
- Correlation risks between positions
- Liquidity risk and cash management

### Position-Level Risks
- Individual position sizes relative to portfolio
- Stop-loss and take-profit level monitoring
- Unrealized losses and drawdown limits
- Position-specific volatility and beta

### Market Risks
- Market volatility and systematic risk
- Interest rate and inflation risks
- Sector rotation and style risks
- Geopolitical and macroeconomic risks

### Operational Risks
- Execution risks and slippage
- Model and data quality risks
- System and technology risks
- Counterparty and broker risks

## Your Risk Thresholds

### Portfolio Limits
- Maximum daily loss: 2% of portfolio value
- Maximum drawdown: 10% from peak
- Maximum position size: 10% of portfolio
- Maximum sector concentration: 30%
- Minimum cash reserve: 5% of portfolio

### Alert Levels
- **GREEN**: All risks within normal parameters
- **YELLOW**: Some risks elevated, monitoring required
- **ORANGE**: Risk thresholds approached, action recommended
- **RED**: Risk limits breached, immediate action required

## Your Decision-Making Process

1. **Monitor**: Continuously track risk metrics and thresholds
2. **Assess**: Evaluate risk levels and potential impact
3. **Alert**: Generate appropriate alerts based on risk levels
4. **Recommend**: Suggest specific risk mitigation actions
5. **Escalate**: Escalate critical risks to other agents
6. **Document**: Record risk events and mitigation actions

## Communication Guidelines

- Use clear risk levels (GREEN/YELLOW/ORANGE/RED)
- Provide specific risk metrics and thresholds
- Recommend concrete actions for risk mitigation
- Prioritize risks by potential impact and probability
- Communicate urgency appropriately

Remember: Your primary goal is capital preservation. It's better to miss opportunities than to take excessive risks that could lead to significant losses.
"""


class RiskControllerAgent(BaseAgent):
    """Risk Controller Agent implementation"""
    
    def __init__(self):
        tools = [
            RiskAnalysisTool(),
            AlertTool(),
            AccountTool(),
            PositionTool(),
            NotificationTool(),
        ]
        
        config = AgentConfig(
            name="risk_controller",
            agent_type=AgentType.RISK_CONTROLLER,
            description="Monitors and controls investment risks",
            system_prompt=RISK_CONTROLLER_SYSTEM_PROMPT,
            model_name=settings.openai.model,
            temperature=0.1,  # Very low temperature for consistent risk assessment
            max_tokens=2000,
            tools=tools,
            update_interval=180,  # 3 minutes - more frequent for risk monitoring
        )
        
        super().__init__(config)
        self.risk_alerts_history = []
        self.last_risk_assessment = None
    
    async def execute_cycle(self) -> Dict[str, Any]:
        """Execute risk monitoring cycle"""
        try:
            logger.info(f"Risk Controller {self.name} starting risk monitoring cycle")
            
            # Step 1: Assess portfolio risks
            portfolio_risk = await self.assess_portfolio_risk()
            
            # Step 2: Check position risks
            position_risks = await self.check_position_risks()
            
            # Step 3: Calculate VaR and risk metrics
            var_analysis = await self.calculate_var()
            
            # Step 4: Generate risk alerts
            risk_alerts = await self.generate_risk_alerts(portfolio_risk, position_risks, var_analysis)
            
            # Step 5: Create risk report
            risk_report = await self.create_risk_report(portfolio_risk, position_risks, var_analysis)
            
            result = {
                "cycle_completed": True,
                "portfolio_risk": portfolio_risk,
                "position_risks": position_risks,
                "var_analysis": var_analysis,
                "risk_alerts": risk_alerts,
                "risk_report": risk_report,
                "timestamp": datetime.now().isoformat(),
            }
            
            logger.info(f"Risk Controller {self.name} completed risk monitoring cycle")
            return result
            
        except Exception as e:
            logger.error(f"Risk Controller {self.name} cycle failed: {e}")
            raise
    
    async def assess_portfolio_risk(self) -> Dict[str, Any]:
        """Assess overall portfolio risk"""
        try:
            # Get portfolio risk analysis
            risk_result = await self.execute_tool(
                "risk_analysis_tool",
                analysis_type="portfolio_risk",
                account_name="default"
            )
            
            if risk_result.status.value == "SUCCESS":
                risk_data = risk_result.data
                
                # Assess risk level based on metrics
                risk_level = self._assess_portfolio_risk_level(risk_data)
                risk_data["overall_risk_level"] = risk_level
                
                return risk_data
            else:
                return {"error": risk_result.error}
                
        except Exception as e:
            logger.error(f"Error assessing portfolio risk: {e}")
            return {"error": str(e)}
    
    def _assess_portfolio_risk_level(self, risk_data: Dict) -> str:
        """Assess overall portfolio risk level"""
        risk_score = 0
        
        # Check concentration risk
        max_position_weight = risk_data.get("max_position_weight", 0)
        if max_position_weight > 20:
            risk_score += 3
        elif max_position_weight > 15:
            risk_score += 2
        elif max_position_weight > 10:
            risk_score += 1
        
        # Check diversification
        diversification_score = risk_data.get("diversification_score", 100)
        if diversification_score < 50:
            risk_score += 3
        elif diversification_score < 70:
            risk_score += 2
        elif diversification_score < 85:
            risk_score += 1
        
        # Check unrealized P&L
        total_unrealized_pnl_pct = risk_data.get("total_unrealized_pnl_percentage", 0)
        if total_unrealized_pnl_pct < -10:
            risk_score += 3
        elif total_unrealized_pnl_pct < -5:
            risk_score += 2
        elif total_unrealized_pnl_pct < -2:
            risk_score += 1
        
        # Determine risk level
        if risk_score >= 6:
            return "RED"
        elif risk_score >= 4:
            return "ORANGE"
        elif risk_score >= 2:
            return "YELLOW"
        else:
            return "GREEN"
    
    async def check_position_risks(self) -> List[Dict]:
        """Check risks for individual positions"""
        position_risks = []
        
        try:
            # Get all positions
            positions_result = await self.execute_tool(
                "position_tool",
                action="get_positions",
                account_name="default"
            )
            
            if positions_result.status.value != "SUCCESS":
                return position_risks
            
            positions = positions_result.data
            
            for position in positions:
                symbol = position["symbol"]
                
                # Analyze position risk
                position_risk_result = await self.execute_tool(
                    "risk_analysis_tool",
                    analysis_type="position_risk",
                    account_name="default",
                    symbol=symbol
                )
                
                if position_risk_result.status.value == "SUCCESS":
                    position_risk_data = position_risk_result.data
                    
                    # Assess position risk level
                    risk_level = self._assess_position_risk_level(position_risk_data)
                    position_risk_data["risk_level"] = risk_level
                    
                    position_risks.append(position_risk_data)
            
            return position_risks
            
        except Exception as e:
            logger.error(f"Error checking position risks: {e}")
            return position_risks
    
    def _assess_position_risk_level(self, position_data: Dict) -> str:
        """Assess individual position risk level"""
        risk_score = 0
        
        # Check position weight
        position_weight = position_data.get("position_weight", 0)
        if position_weight > 15:
            risk_score += 3
        elif position_weight > 10:
            risk_score += 2
        elif position_weight > 7:
            risk_score += 1
        
        # Check unrealized P&L
        unrealized_pnl_pct = position_data.get("unrealized_pnl_percentage", 0)
        if unrealized_pnl_pct < -15:
            risk_score += 3
        elif unrealized_pnl_pct < -10:
            risk_score += 2
        elif unrealized_pnl_pct < -5:
            risk_score += 1
        
        # Check stop loss distance
        stop_loss_distance = position_data.get("stop_loss_distance_pct", 0)
        if stop_loss_distance > 10:
            risk_score += 2
        elif stop_loss_distance > 7:
            risk_score += 1
        
        # Determine risk level
        if risk_score >= 5:
            return "RED"
        elif risk_score >= 3:
            return "ORANGE"
        elif risk_score >= 1:
            return "YELLOW"
        else:
            return "GREEN"
    
    async def calculate_var(self) -> Dict[str, Any]:
        """Calculate Value at Risk"""
        try:
            var_result = await self.execute_tool(
                "risk_analysis_tool",
                analysis_type="var_analysis",
                account_name="default",
                confidence_level=0.95,
                time_horizon=1
            )
            
            if var_result.status.value == "SUCCESS":
                return var_result.data
            else:
                return {"error": var_result.error}
                
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return {"error": str(e)}
    
    async def generate_risk_alerts(self, portfolio_risk: Dict, position_risks: List[Dict], var_analysis: Dict) -> List[Dict]:
        """Generate risk alerts based on analysis"""
        alerts_generated = []
        
        try:
            # Portfolio-level alerts
            portfolio_risk_level = portfolio_risk.get("overall_risk_level", "GREEN")
            
            if portfolio_risk_level in ["ORANGE", "RED"]:
                alert_level = "WARNING" if portfolio_risk_level == "ORANGE" else "ERROR"
                
                alert_result = await self.execute_tool(
                    "alert_tool",
                    alert_type="risk_alert",
                    level=alert_level,
                    title=f"Portfolio Risk Level: {portfolio_risk_level}",
                    message=f"Portfolio risk level is {portfolio_risk_level}. Review risk metrics and consider risk reduction.",
                    data=portfolio_risk,
                    agent_name=self.name
                )
                
                if alert_result.status.value == "SUCCESS":
                    alerts_generated.append(alert_result.data)
            
            # Position-level alerts
            high_risk_positions = [p for p in position_risks if p.get("risk_level") in ["ORANGE", "RED"]]
            
            for position in high_risk_positions:
                alert_level = "WARNING" if position["risk_level"] == "ORANGE" else "ERROR"
                
                alert_result = await self.execute_tool(
                    "alert_tool",
                    alert_type="risk_alert",
                    level=alert_level,
                    title=f"High Risk Position: {position['symbol']}",
                    message=f"Position {position['symbol']} has {position['risk_level']} risk level",
                    data=position,
                    symbol=position["symbol"],
                    agent_name=self.name
                )
                
                if alert_result.status.value == "SUCCESS":
                    alerts_generated.append(alert_result.data)
            
            # VaR alerts
            var_percentage = var_analysis.get("var_percentage", 0)
            if var_percentage > 5:  # More than 5% VaR
                alert_result = await self.execute_tool(
                    "alert_tool",
                    alert_type="risk_alert",
                    level="WARNING",
                    title=f"High Value at Risk: {var_percentage:.2f}%",
                    message=f"Portfolio VaR is {var_percentage:.2f}%, exceeding 5% threshold",
                    data=var_analysis,
                    agent_name=self.name
                )
                
                if alert_result.status.value == "SUCCESS":
                    alerts_generated.append(alert_result.data)
            
            # Update alerts history
            self.risk_alerts_history.extend(alerts_generated)
            if len(self.risk_alerts_history) > 100:
                self.risk_alerts_history = self.risk_alerts_history[-50:]
            
            return alerts_generated
            
        except Exception as e:
            logger.error(f"Error generating risk alerts: {e}")
            return alerts_generated
    
    async def create_risk_report(self, portfolio_risk: Dict, position_risks: List[Dict], var_analysis: Dict) -> Dict[str, Any]:
        """Create comprehensive risk report"""
        try:
            # Count risk levels
            risk_level_counts = {"GREEN": 0, "YELLOW": 0, "ORANGE": 0, "RED": 0}
            
            for position in position_risks:
                risk_level = position.get("risk_level", "GREEN")
                risk_level_counts[risk_level] += 1
            
            # Create report
            report = {
                "report_date": datetime.now().isoformat(),
                "portfolio_risk_level": portfolio_risk.get("overall_risk_level", "GREEN"),
                "portfolio_metrics": {
                    "max_position_weight": portfolio_risk.get("max_position_weight", 0),
                    "diversification_score": portfolio_risk.get("diversification_score", 0),
                    "concentration_risk": portfolio_risk.get("concentration_risk", "LOW"),
                    "total_unrealized_pnl_pct": portfolio_risk.get("total_unrealized_pnl_percentage", 0),
                },
                "var_metrics": {
                    "var_amount": var_analysis.get("var_amount", 0),
                    "var_percentage": var_analysis.get("var_percentage", 0),
                    "confidence_level": var_analysis.get("confidence_level", 0.95),
                },
                "position_risk_summary": risk_level_counts,
                "high_risk_positions": [p["symbol"] for p in position_risks if p.get("risk_level") in ["ORANGE", "RED"]],
                "recommendations": self._generate_risk_recommendations(portfolio_risk, position_risks, var_analysis),
            }
            
            # Send risk report notification
            await self.execute_tool(
                "notification_tool",
                notification_type="risk_report",
                title=f"Risk Report - Portfolio Risk: {report['portfolio_risk_level']}",
                message=f"Portfolio risk level: {report['portfolio_risk_level']}, VaR: {report['var_metrics']['var_percentage']:.2f}%",
                data=report,
                priority="HIGH" if report['portfolio_risk_level'] in ["ORANGE", "RED"] else "NORMAL"
            )
            
            self.last_risk_assessment = report
            return report
            
        except Exception as e:
            logger.error(f"Error creating risk report: {e}")
            return {"error": str(e)}
    
    def _generate_risk_recommendations(self, portfolio_risk: Dict, position_risks: List[Dict], var_analysis: Dict) -> List[str]:
        """Generate risk mitigation recommendations"""
        recommendations = []
        
        # Portfolio-level recommendations
        max_position_weight = portfolio_risk.get("max_position_weight", 0)
        if max_position_weight > 15:
            recommendations.append(f"Reduce largest position size from {max_position_weight:.1f}% to below 15%")
        
        diversification_score = portfolio_risk.get("diversification_score", 100)
        if diversification_score < 70:
            recommendations.append("Improve diversification by adding positions in different sectors")
        
        # Position-level recommendations
        high_risk_positions = [p for p in position_risks if p.get("risk_level") in ["ORANGE", "RED"]]
        if high_risk_positions:
            recommendations.append(f"Review {len(high_risk_positions)} high-risk positions for potential risk reduction")
        
        # VaR recommendations
        var_percentage = var_analysis.get("var_percentage", 0)
        if var_percentage > 5:
            recommendations.append("Consider reducing portfolio volatility to lower Value at Risk")
        
        # Cash recommendations
        cash_pct = portfolio_risk.get("cash_percentage", 0)
        if cash_pct < 5:
            recommendations.append("Increase cash reserves to at least 5% for risk management")
        
        return recommendations
