"""
Configuration management for Stock AI Agents system
"""

import os
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """Database configuration settings"""

    url: str = Field(default="postgresql://localhost:5432/stock_ai")
    host: str = Field(default="localhost")
    port: int = Field(default=5432)
    name: str = Field(default="stock_ai")
    user: str = Field(default="postgres")
    password: str = Field(default="")

    class Config:
        env_prefix = "DATABASE_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class OpenAISettings(BaseSettings):
    """OpenAI API configuration settings"""

    api_key: str = Field(...)
    base_url: str = Field(default="https://api.openai.com/v1")
    model: str = Field(default="gpt-4")
    max_tokens: int = Field(default=2000)
    temperature: float = Field(default=0.7)

    class Config:
        env_prefix = "OPENAI_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class TradingSettings(BaseSettings):
    """Trading configuration settings"""

    mode: str = Field(default="SIMULATION")  # SIMULATION or LIVE
    initial_capital: float = Field(default=1000000.0)  # 100万人民币
    max_position_size: float = Field(default=0.1)
    stop_loss_percentage: float = Field(default=0.05)
    take_profit_percentage: float = Field(default=0.15)
    max_daily_loss: float = Field(default=0.02)
    max_drawdown: float = Field(default=0.1)
    risk_free_rate: float = Field(default=0.03)

    class Config:
        env_prefix = "TRADING_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class MarketDataSettings(BaseSettings):
    """Market data configuration settings"""

    provider: str = Field(default="yahoo")
    alpha_vantage_api_key: Optional[str] = Field(default=None)
    update_interval: int = Field(default=300)  # seconds

    class Config:
        env_prefix = "MARKET_DATA_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class AgentSettings(BaseSettings):
    """Agent configuration settings"""

    update_interval: int = Field(default=60)  # seconds
    decision_cooldown: int = Field(default=300)  # seconds
    max_concurrent_agents: int = Field(default=5)

    class Config:
        env_prefix = "AGENT_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class MarketHoursSettings(BaseSettings):
    """Market hours configuration"""

    open_hour: int = Field(default=14)  # UTC
    open_minute: int = Field(default=30)
    close_hour: int = Field(default=21)  # UTC
    close_minute: int = Field(default=0)

    class Config:
        env_prefix = "MARKET_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class LoggingSettings(BaseSettings):
    """Logging configuration settings"""

    level: str = Field(default="INFO")
    file: str = Field(default="logs/stock_ai.log")
    max_size: str = Field(default="10MB")
    backup_count: int = Field(default=5)

    class Config:
        env_prefix = "LOG_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class NotificationSettings(BaseSettings):
    """Notification configuration settings"""

    enabled: bool = Field(default=True)
    webhook_url: Optional[str] = Field(default=None)
    level: str = Field(default="WARNING")

    class Config:
        env_prefix = "NOTIFICATION_"
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


class Settings(BaseSettings):
    """Main application settings"""
    
    debug: bool = Field(default=False, env="DEBUG")
    testing: bool = Field(default=False, env="TESTING")
    mock_market_data: bool = Field(default=False, env="MOCK_MARKET_DATA")
    
    # Sub-settings
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    openai: OpenAISettings = Field(default_factory=OpenAISettings)
    trading: TradingSettings = Field(default_factory=TradingSettings)
    market_data: MarketDataSettings = Field(default_factory=MarketDataSettings)
    agents: AgentSettings = Field(default_factory=AgentSettings)
    market_hours: MarketHoursSettings = Field(default_factory=MarketHoursSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    notifications: NotificationSettings = Field(default_factory=NotificationSettings)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"


# Global settings instance
settings = Settings()
