"""
Account and trading related database models
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Column, String, Numeric, DateTime, ForeignKey, 
    Integer, Boolean, Text, Enum as SQLEnum
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class TransactionType(str, Enum):
    """Transaction types"""
    BUY = "BUY"
    SELL = "SELL"
    DIVIDEND = "DIVIDEND"
    DEPOSIT = "DEPOSIT"
    WITHDRAWAL = "WITHDRAWAL"


class TransactionStatus(str, Enum):
    """Transaction status"""
    PENDING = "PENDING"
    EXECUTED = "EXECUTED"
    CANCELLED = "CANCELLED"
    FAILED = "FAILED"


class Account(BaseModel):
    """Stock trading account model"""
    
    __tablename__ = "accounts"
    
    name = Column(String(100), nullable=False, unique=True)
    account_type = Column(String(50), nullable=False, default="TRADING")  # TRADING, SIMULATION
    initial_capital = Column(Numeric(15, 2), nullable=False)
    current_cash = Column(Numeric(15, 2), nullable=False)
    total_value = Column(Numeric(15, 2), nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Risk management
    max_position_size = Column(Numeric(5, 4), default=0.1)  # 10% max position
    stop_loss_percentage = Column(Numeric(5, 4), default=0.05)  # 5% stop loss
    take_profit_percentage = Column(Numeric(5, 4), default=0.15)  # 15% take profit
    
    # Relationships
    transactions = relationship("Transaction", back_populates="account", cascade="all, delete-orphan")
    positions = relationship("Position", back_populates="account", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Account(name='{self.name}', cash={self.current_cash}, value={self.total_value})>"


class Transaction(BaseModel):
    """Transaction record model"""
    
    __tablename__ = "transactions"
    
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=False)
    stock_symbol = Column(String(20), nullable=True)  # Null for cash transactions
    transaction_type = Column(SQLEnum(TransactionType), nullable=False)
    status = Column(SQLEnum(TransactionStatus), default=TransactionStatus.PENDING, nullable=False)
    
    quantity = Column(Numeric(15, 4), nullable=True)  # Null for cash transactions
    price = Column(Numeric(10, 4), nullable=True)  # Price per share
    amount = Column(Numeric(15, 2), nullable=False)  # Total transaction amount
    fees = Column(Numeric(10, 2), default=0, nullable=False)
    
    executed_at = Column(DateTime(timezone=True), nullable=True)
    notes = Column(Text, nullable=True)
    
    # Decision tracking
    decision_id = Column(Integer, ForeignKey("decisions.id"), nullable=True)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=True)
    
    # Relationships
    account = relationship("Account", back_populates="transactions")
    decision = relationship("Decision", back_populates="transactions")
    agent = relationship("Agent", back_populates="transactions")
    
    def __repr__(self) -> str:
        return f"<Transaction({self.transaction_type} {self.quantity} {self.stock_symbol} @ {self.price})>"


class Position(BaseModel):
    """Current stock position model"""
    
    __tablename__ = "positions"
    
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=False)
    stock_symbol = Column(String(20), nullable=False)
    
    quantity = Column(Numeric(15, 4), nullable=False)
    average_cost = Column(Numeric(10, 4), nullable=False)
    current_price = Column(Numeric(10, 4), nullable=True)
    market_value = Column(Numeric(15, 2), nullable=True)
    
    unrealized_pnl = Column(Numeric(15, 2), nullable=True)
    unrealized_pnl_percentage = Column(Numeric(8, 4), nullable=True)
    
    # Risk management
    stop_loss_price = Column(Numeric(10, 4), nullable=True)
    take_profit_price = Column(Numeric(10, 4), nullable=True)
    
    is_active = Column(Boolean, default=True, nullable=False)
    last_updated = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    account = relationship("Account", back_populates="positions")
    
    def __repr__(self) -> str:
        return f"<Position({self.stock_symbol}: {self.quantity} @ {self.average_cost})>"
