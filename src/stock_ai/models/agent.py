"""
Agent related database models
"""

from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Column, String, DateTime, <PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, 
    <PERSON><PERSON><PERSON>, Text, JSON, Enum as SQLEnum
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class AgentType(str, Enum):
    """Agent types"""
    MARKET_ANALYST = "MARKET_ANALYST"
    STOCK_SELECTOR = "STOCK_SELECTOR"
    TRADING_DECISION_MAKER = "TRADING_DECISION_MAKER"
    PORTFOLIO_MANAGER = "PORTFOLIO_MANAGER"
    RISK_CONTROLLER = "RISK_CONTROLLER"


class AgentStatus(str, Enum):
    """Agent status"""
    ACTIVE = "ACTIVE"
    INACTIVE = "INACTIVE"
    PAUSED = "PAUSED"
    ERROR = "ERROR"


class MessageType(str, Enum):
    """Message types"""
    SYSTEM = "SYSTEM"
    USER = "USER"
    ASSISTANT = "ASSISTANT"
    TOOL_CALL = "TOOL_CALL"
    TOOL_RESULT = "TOOL_RESULT"


class Agent(BaseModel):
    """Agent model"""
    
    __tablename__ = "agents"
    
    name = Column(String(100), nullable=False, unique=True)
    agent_type = Column(SQLEnum(AgentType), nullable=False)
    status = Column(SQLEnum(AgentStatus), default=AgentStatus.ACTIVE, nullable=False)
    
    description = Column(Text, nullable=True)
    system_prompt = Column(Text, nullable=False)
    model_name = Column(String(100), nullable=False, default="gpt-4")
    
    # Configuration
    temperature = Column(String(10), default="0.7", nullable=False)
    max_tokens = Column(Integer, default=2000, nullable=False)
    tools_config = Column(JSON, default=list, nullable=False)
    
    # Runtime state
    last_active_at = Column(DateTime(timezone=True), nullable=True)
    last_error = Column(Text, nullable=True)
    execution_count = Column(Integer, default=0, nullable=False)
    
    # Relationships
    messages = relationship("AgentMessage", back_populates="agent", cascade="all, delete-orphan")
    states = relationship("AgentState", back_populates="agent", cascade="all, delete-orphan")
    transactions = relationship("Transaction", back_populates="agent")
    decisions = relationship("Decision", back_populates="agent")
    
    def __repr__(self) -> str:
        return f"<Agent(name='{self.name}', type={self.agent_type}, status={self.status})>"


class AgentMessage(BaseModel):
    """Agent conversation message model"""
    
    __tablename__ = "agent_messages"
    
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)
    session_id = Column(String(100), nullable=False)  # Conversation session ID
    
    message_type = Column(SQLEnum(MessageType), nullable=False)
    content = Column(Text, nullable=False)
    
    # Tool call information
    tool_name = Column(String(100), nullable=True)
    tool_arguments = Column(JSON, nullable=True)
    tool_result = Column(JSON, nullable=True)
    
    # Token usage
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True)
    total_tokens = Column(Integer, nullable=True)
    
    # Relationships
    agent = relationship("Agent", back_populates="messages")
    
    def __repr__(self) -> str:
        return f"<AgentMessage(agent={self.agent_id}, type={self.message_type})>"


class AgentState(BaseModel):
    """Agent state snapshot model"""
    
    __tablename__ = "agent_states"
    
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)
    state_name = Column(String(100), nullable=False)  # e.g., "current_analysis", "pending_decisions"
    state_data = Column(JSON, nullable=False)
    
    is_current = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    
    # Relationships
    agent = relationship("Agent", back_populates="states")
    
    def __repr__(self) -> str:
        return f"<AgentState(agent={self.agent_id}, name='{self.state_name}')>"
