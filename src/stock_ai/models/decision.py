"""
Decision and planning related database models
"""

from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional

from sqlalchemy import (
    Column, String, Numeric, DateTime, ForeignKey, 
    Integer, Boolean, Text, JSON, Enum as SQLEnum
)
from sqlalchemy.orm import relationship

from .base import BaseModel


class DecisionType(str, Enum):
    """Decision types"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    REBALANCE = "REBALANCE"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"


class DecisionStatus(str, Enum):
    """Decision status"""
    PENDING = "PENDING"
    APPROVED = "APPROVED"
    EXECUTED = "EXECUTED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"


class PlanStatus(str, Enum):
    """Plan status"""
    ACTIVE = "ACTIVE"
    COMPLETED = "COMPLETED"
    CANCELLED = "CANCELLED"
    PAUSED = "PAUSED"


class Decision(BaseModel):
    """Trading decision model"""
    
    __tablename__ = "decisions"
    
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=False)
    
    decision_type = Column(SQLEnum(DecisionType), nullable=False)
    status = Column(SQLEnum(DecisionStatus), default=DecisionStatus.PENDING, nullable=False)
    
    # Stock and quantity
    stock_symbol = Column(String(20), nullable=False)
    quantity = Column(Numeric(15, 4), nullable=True)
    target_price = Column(Numeric(10, 4), nullable=True)
    stop_loss_price = Column(Numeric(10, 4), nullable=True)
    take_profit_price = Column(Numeric(10, 4), nullable=True)
    
    # Decision rationale
    reasoning = Column(Text, nullable=False)
    confidence_score = Column(Numeric(3, 2), nullable=True)  # 0.00 to 1.00
    risk_assessment = Column(Text, nullable=True)
    
    # Market context
    market_price_at_decision = Column(Numeric(10, 4), nullable=True)
    market_conditions = Column(JSON, nullable=True)
    technical_indicators = Column(JSON, nullable=True)
    
    # Execution tracking
    approved_at = Column(DateTime(timezone=True), nullable=True)
    executed_at = Column(DateTime(timezone=True), nullable=True)
    execution_price = Column(Numeric(10, 4), nullable=True)
    execution_notes = Column(Text, nullable=True)
    
    # File references
    decision_file_path = Column(String(500), nullable=True)  # Path to MD file
    
    # Relationships
    agent = relationship("Agent", back_populates="decisions")
    account = relationship("Account")
    transactions = relationship("Transaction", back_populates="decision")
    
    def __repr__(self) -> str:
        return f"<Decision({self.decision_type} {self.quantity} {self.stock_symbol} @ {self.target_price})>"


class Plan(BaseModel):
    """Investment plan model"""
    
    __tablename__ = "plans"
    
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)
    account_id = Column(Integer, ForeignKey("accounts.id"), nullable=False)
    
    name = Column(String(200), nullable=False)
    description = Column(Text, nullable=False)
    status = Column(SQLEnum(PlanStatus), default=PlanStatus.ACTIVE, nullable=False)
    
    # Time frame
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=True)
    review_frequency = Column(String(50), nullable=False, default="WEEKLY")  # DAILY, WEEKLY, MONTHLY
    
    # Objectives
    target_return = Column(Numeric(6, 4), nullable=True)  # Target return percentage
    max_risk = Column(Numeric(6, 4), nullable=True)      # Maximum risk tolerance
    
    # Strategy
    strategy_type = Column(String(100), nullable=False)  # GROWTH, VALUE, MOMENTUM, etc.
    asset_allocation = Column(JSON, nullable=True)       # Target allocation by sector/asset
    
    # Performance tracking
    initial_value = Column(Numeric(15, 2), nullable=True)
    current_value = Column(Numeric(15, 2), nullable=True)
    realized_return = Column(Numeric(8, 4), nullable=True)
    unrealized_return = Column(Numeric(8, 4), nullable=True)
    
    # File references
    plan_file_path = Column(String(500), nullable=True)  # Path to MD file
    
    # Relationships
    agent = relationship("Agent")
    account = relationship("Account")
    updates = relationship("PlanUpdate", back_populates="plan", cascade="all, delete-orphan")
    
    def __repr__(self) -> str:
        return f"<Plan(name='{self.name}', status={self.status})>"


class PlanUpdate(BaseModel):
    """Plan update/revision model"""
    
    __tablename__ = "plan_updates"
    
    plan_id = Column(Integer, ForeignKey("plans.id"), nullable=False)
    agent_id = Column(Integer, ForeignKey("agents.id"), nullable=False)
    
    update_type = Column(String(50), nullable=False)  # REVIEW, ADJUSTMENT, MILESTONE, etc.
    summary = Column(String(500), nullable=False)
    details = Column(Text, nullable=False)
    
    # Changes made
    changes_made = Column(JSON, nullable=True)
    previous_values = Column(JSON, nullable=True)
    new_values = Column(JSON, nullable=True)
    
    # Performance at update
    portfolio_value = Column(Numeric(15, 2), nullable=True)
    return_since_start = Column(Numeric(8, 4), nullable=True)
    return_since_last_update = Column(Numeric(8, 4), nullable=True)
    
    # File references
    update_file_path = Column(String(500), nullable=True)  # Path to MD file
    
    # Relationships
    plan = relationship("Plan", back_populates="updates")
    agent = relationship("Agent")
    
    def __repr__(self) -> str:
        return f"<PlanUpdate(plan_id={self.plan_id}, type='{self.update_type}')>"
