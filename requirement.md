# 炒股群组智能体系统需求

## 系统概述
炒股群组智能体是一种多智能体模式，且允许人工介入。该群组智能体的目标是：在规定时间内（半个月、1个月、3个月、半年等），通过股票市场的运作，将'股票账户'收益最大化(通俗讲：使账户总资金变的尽可能多)。

## 技术要求
- 请选用主流编程语言和框架，开发出该智能体群组模式
- 由你决策智能体的角色构成（如：选股智能体、买卖决策智能体、市场分析智能体等）
- 你需要为各智能体编写好系统提示词，工具列表（工具可以给出需求列表，而不必完全用代码实现，我可以在后续自行丰富）
- 可以仅在终端中运行（不必UI界面，但是终端输出时需要有不同颜色的输出）

## 智能体要求
- 做出具体买卖决策时，要调用一些工具记录下决策依据
- 要借助工具维护一份计划，计划应该是动态调整的
- 智能体使用的模型优先采用OpenAI兼容的接口
- 智能体使用的工具：支持MCP、支持自定义

## 数据存储要求
- '股票账户' 需要被开发成工具，记录了购买记录，持仓股票代码，买入份额、份额单价、持仓价格等，且可以被更新
- 数据如需通过关系型数据库持久化，请使用postgresql
- 决策依据的记录、投资计划的记录可以采用md文件
- 智能体的消息如需持久化记录，也采用postgresql

## 运行模式
- 智能体应该是持续运行的，即不需要人工消息驱动
- 智能体可以借助工具进入暂停状态，比如周期性盯盘工具（可查询特定股票代码实时价格，在特定系统时间才返回），停盘（如今天交易结束，明天开盘或开盘前10分钟工具返回）
- 通过这类阻塞式的工具调用来实现系统暂停