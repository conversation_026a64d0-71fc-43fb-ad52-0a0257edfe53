#!/bin/bash

# Stock AI Agents - 中国A股智能体交易系统 安装脚本
# 自动化安装和配置脚本

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Python版本
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3 未安装，请先安装 Python 3.9+"
        exit 1
    fi
    
    python_version=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
    if [[ $(echo "$python_version < 3.9" | bc -l) -eq 1 ]]; then
        log_error "Python 版本过低 ($python_version)，需要 Python 3.9+"
        exit 1
    fi
    log_success "Python 版本检查通过: $python_version"
    
    # 检查PostgreSQL
    if ! command -v psql &> /dev/null; then
        log_warning "PostgreSQL 未安装，将尝试自动安装..."
        install_postgresql
    else
        log_success "PostgreSQL 已安装"
    fi
    
    # 检查内存
    total_mem=$(free -m | awk 'NR==2{printf "%.0f", $2/1024}')
    if [[ $total_mem -lt 4 ]]; then
        log_warning "系统内存少于4GB，可能影响性能"
    else
        log_success "内存检查通过: ${total_mem}GB"
    fi
}

# 安装PostgreSQL
install_postgresql() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        if command -v apt-get &> /dev/null; then
            log_info "在Ubuntu/Debian上安装PostgreSQL..."
            sudo apt-get update
            sudo apt-get install -y postgresql postgresql-contrib
        elif command -v yum &> /dev/null; then
            log_info "在CentOS/RHEL上安装PostgreSQL..."
            sudo yum install -y postgresql-server postgresql-contrib
            sudo postgresql-setup initdb
        fi
        sudo systemctl start postgresql
        sudo systemctl enable postgresql
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        if command -v brew &> /dev/null; then
            log_info "在macOS上安装PostgreSQL..."
            brew install postgresql
            brew services start postgresql
        else
            log_error "请先安装Homebrew: https://brew.sh/"
            exit 1
        fi
    else
        log_error "不支持的操作系统，请手动安装PostgreSQL"
        exit 1
    fi
    log_success "PostgreSQL 安装完成"
}

# 创建虚拟环境
setup_venv() {
    log_info "创建Python虚拟环境..."
    
    if [[ -d "venv" ]]; then
        log_warning "虚拟环境已存在，跳过创建"
    else
        python3 -m venv venv
        log_success "虚拟环境创建完成"
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    log_info "虚拟环境已激活"
    
    # 升级pip
    pip install --upgrade pip
    log_success "pip 已升级到最新版本"
}

# 安装Python依赖
install_dependencies() {
    log_info "安装Python依赖包..."
    
    # 安装项目依赖
    pip install -e .
    log_success "项目依赖安装完成"
}

# 配置数据库
setup_database() {
    log_info "配置数据库..."
    
    # 提示用户输入数据库配置
    read -p "请输入数据库用户名 (默认: stock_ai_user): " db_user
    db_user=${db_user:-stock_ai_user}
    
    read -s -p "请输入数据库密码: " db_password
    echo
    
    if [[ -z "$db_password" ]]; then
        log_error "数据库密码不能为空"
        exit 1
    fi
    
    # 创建数据库用户和数据库
    log_info "创建数据库用户和数据库..."
    sudo -u postgres psql << EOF
CREATE USER $db_user WITH PASSWORD '$db_password';
CREATE DATABASE stock_ai OWNER $db_user;
GRANT ALL PRIVILEGES ON DATABASE stock_ai TO $db_user;
\q
EOF
    
    log_success "数据库配置完成"
    
    # 保存数据库配置到环境变量
    DB_URL="postgresql://$db_user:$db_password@localhost:5432/stock_ai"
    echo "DATABASE_URL=$DB_URL" >> .env.tmp
    echo "DATABASE_HOST=localhost" >> .env.tmp
    echo "DATABASE_PORT=5432" >> .env.tmp
    echo "DATABASE_NAME=stock_ai" >> .env.tmp
    echo "DATABASE_USER=$db_user" >> .env.tmp
    echo "DATABASE_PASSWORD=$db_password" >> .env.tmp
}

# 配置环境变量
setup_env() {
    log_info "配置环境变量..."
    
    if [[ -f ".env" ]]; then
        log_warning ".env 文件已存在，将备份为 .env.backup"
        cp .env .env.backup
    fi
    
    # 复制示例配置文件
    if [[ -f ".env.example" ]]; then
        cp .env.example .env
    else
        # 创建基本的.env文件
        cat > .env << 'EOF'
# OpenAI API配置（必需）
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=2000
OPENAI_TEMPERATURE=0.7

# 交易配置 - 中国A股市场
TRADING_MODE=SIMULATION
INITIAL_CAPITAL=1000000.0
MAX_POSITION_SIZE=0.1
STOP_LOSS_PERCENTAGE=0.05
TAKE_PROFIT_PERCENTAGE=0.15
MAX_DAILY_LOSS=0.02
MAX_DRAWDOWN=0.1
RISK_FREE_RATE=0.03

# 市场数据配置
MARKET_DATA_PROVIDER=yahoo
UPDATE_INTERVAL=300

# 智能体配置
AGENT_UPDATE_INTERVAL=60
DECISION_COOLDOWN=300
MAX_CONCURRENT_AGENTS=5

# 中国A股交易时间（UTC时间）
MARKET_OPEN_HOUR=1
MARKET_OPEN_MINUTE=30
MARKET_CLOSE_HOUR=7
MARKET_CLOSE_MINUTE=0

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/stock_ai.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# 通知配置
ENABLE_NOTIFICATIONS=true
NOTIFICATION_LEVEL=WARNING
EOF
    fi
    
    # 合并数据库配置
    if [[ -f ".env.tmp" ]]; then
        cat .env.tmp >> .env
        rm .env.tmp
    fi
    
    log_success "环境变量配置完成"
    
    # 提示用户配置OpenAI API密钥
    log_warning "请编辑 .env 文件，设置您的 OpenAI API 密钥："
    log_info "OPENAI_API_KEY=your_actual_api_key_here"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 初始化数据库
    python -c "
from src.stock_ai.database import init_database
init_database()
print('数据库初始化完成')
"
    
    log_success "数据库初始化完成"
}

# 测试安装
test_installation() {
    log_info "测试安装..."
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 测试数据库连接
    python -c "
from src.stock_ai.database import get_db_session
try:
    session = next(get_db_session())
    session.close()
    print('数据库连接测试成功')
except Exception as e:
    print(f'数据库连接测试失败: {e}')
    exit(1)
"
    
    log_success "安装测试通过"
}

# 主函数
main() {
    echo "=================================================="
    echo "Stock AI Agents - 中国A股智能体交易系统"
    echo "自动安装脚本"
    echo "=================================================="
    echo
    
    # 检查是否在项目根目录
    if [[ ! -f "pyproject.toml" ]]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 执行安装步骤
    check_requirements
    setup_venv
    install_dependencies
    setup_database
    setup_env
    init_database
    test_installation
    
    echo
    echo "=================================================="
    log_success "安装完成！"
    echo "=================================================="
    echo
    log_info "下一步操作："
    echo "1. 编辑 .env 文件，设置您的 OpenAI API 密钥"
    echo "2. 运行系统: source venv/bin/activate && stock-ai start"
    echo "3. 查看日志: tail -f logs/stock_ai.log"
    echo
    log_warning "注意：系统默认运行在模拟模式，不会进行真实交易"
}

# 运行主函数
main "$@"
