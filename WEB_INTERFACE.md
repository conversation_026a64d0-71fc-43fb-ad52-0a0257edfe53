# Stock AI Agents Web Interface

这是一个现代化的Web界面，用于管理和监控多智能体股票交易系统。

## 功能特性

### 🎯 主要功能

1. **实时仪表盘** - 系统状态、投资组合表现、智能体活动的实时监控
2. **智能体管理** - 查看、启动、停止和监控各个AI智能体
3. **投资组合分析** - 详细的持仓信息、风险指标和表现分析
4. **市场分析** - 实时市场数据、新闻资讯、情绪分析和经济指标
5. **系统设置** - 配置交易参数、风险控制和通知设置

### 🚀 技术特性

- **响应式设计** - 支持桌面和移动设备
- **实时更新** - 自动刷新数据，保持信息最新
- **现代UI** - 基于Bootstrap 5的美观界面
- **交互式图表** - 使用Chart.js展示数据可视化
- **RESTful API** - 完整的API接口支持

## 快速开始

### 启动Web服务器

```bash
# 激活虚拟环境
source .venv/bin/activate

# 启动Web界面
stock-ai web --host 0.0.0.0 --port 8000

# 或者使用开发模式（支持热重载）
stock-ai web --host 0.0.0.0 --port 8000 --reload
```

### 访问界面

- **主界面**: http://localhost:8000
- **API文档**: http://localhost:8000/api/docs
- **ReDoc文档**: http://localhost:8000/api/redoc

## 界面导航

### 1. 仪表盘 (/)
- **系统状态卡片**: 显示系统运行状态、活跃智能体数量等
- **投资组合概览**: 总资产、现金余额、今日盈亏等关键指标
- **表现图表**: 投资组合价值变化趋势
- **资产配置**: 饼图显示资产分布
- **智能体状态**: 各智能体的运行状态和执行统计
- **市场资讯**: 最新的市场新闻和分析
- **系统日志**: 最近的系统活动记录

### 2. 智能体管理 (/agents)
- **智能体概览**: 总数、运行中、暂停、停止的智能体统计
- **批量操作**: 一键启动/停止所有智能体
- **智能体列表**: 详细的智能体信息表格
  - 名称和描述
  - 类型和状态
  - 执行次数和错误统计
  - 最后执行时间
  - 操作按钮（启动/停止/详情）
- **智能体详情**: 弹窗显示详细信息和操作选项

### 3. 投资组合 (/portfolio)
- **资产概览**: 总资产、现金、盈亏等关键指标
- **表现图表**: 可选择不同时间范围的表现曲线
- **持仓明细**: 详细的股票持仓信息
  - 股票代码和名称
  - 持仓数量和成本价
  - 当前价格和市值
  - 盈亏金额和比例
  - 权重分布
- **资产配置**: 饼图和图例显示资产分布
- **风险指标**: 年化波动率、夏普比率、最大回撤、Beta系数

### 4. 市场分析 (/market)
- **市场概览**: 主要指数的实时数据和涨跌情况
- **多标签页面**:
  - **自选股**: 关注股票的实时价格和涨跌
  - **市场资讯**: 最新新闻和情绪分析
  - **情绪分析**: 市场整体情绪和分布统计
  - **经济指标**: GDP等宏观经济数据
- **行业热力图**: 各行业板块的表现情况

### 5. 系统设置 (/settings)
- **系统设置**: 运行模式、协调模式、更新间隔等
- **AI模型配置**: 模型选择、参数调整
- **交易设置**: 资金配置、仓位限制、交易频率等
- **风险控制**: 止损止盈、回撤限制、风险监控
- **通知设置**: 邮件、短信通知配置

## API接口

### 系统状态
- `GET /api/status` - 获取系统状态
- `POST /api/system/start` - 启动系统
- `POST /api/system/stop` - 停止系统

### 智能体管理
- `GET /api/agents` - 获取所有智能体状态
- `POST /api/agents/{agent_name}/start` - 启动指定智能体
- `POST /api/agents/{agent_name}/stop` - 停止指定智能体

### 投资组合
- `GET /api/portfolio` - 获取投资组合概览
- `GET /api/portfolio/positions` - 获取持仓明细

### 市场数据
- `GET /api/market/news` - 获取市场新闻
- `GET /api/market/sentiment` - 获取市场情绪分析
- `GET /api/market/economic` - 获取经济指标
- `GET /api/market/watchlist` - 获取自选股数据

### 系统日志
- `GET /api/logs` - 获取系统日志

## 开发说明

### 技术栈
- **后端**: FastAPI + Python
- **前端**: HTML5 + Bootstrap 5 + JavaScript
- **图表**: Chart.js
- **图标**: Bootstrap Icons
- **模板引擎**: Jinja2

### 文件结构
```
src/stock_ai/web/
├── __init__.py          # Web模块初始化
├── app.py              # FastAPI应用配置
├── routes.py           # API路由定义
├── static/             # 静态文件（CSS、JS、图片）
└── templates/          # HTML模板
    ├── base.html       # 基础模板
    ├── dashboard.html  # 仪表盘页面
    ├── agents.html     # 智能体管理页面
    ├── portfolio.html  # 投资组合页面
    ├── market.html     # 市场分析页面
    └── settings.html   # 设置页面
```

### 自定义开发
1. **添加新页面**: 在templates目录创建HTML模板，在routes.py添加路由
2. **修改样式**: 编辑base.html中的CSS或添加新的静态文件
3. **扩展API**: 在routes.py中添加新的API端点
4. **集成真实数据**: 替换mock数据为实际的数据库查询或服务调用

## 注意事项

1. **开发模式**: 当前使用模拟数据，生产环境需要连接实际的数据源
2. **安全性**: 生产环境需要添加认证和授权机制
3. **性能**: 大量数据时需要考虑分页和缓存
4. **监控**: 建议添加应用性能监控和错误追踪

## 故障排除

### 常见问题

1. **端口被占用**: 更改端口号或停止占用端口的进程
2. **依赖缺失**: 确保安装了所有必需的Python包
3. **模板不显示**: 检查模板路径和静态文件路径配置
4. **API调用失败**: 查看浏览器开发者工具的网络面板

### 日志查看
Web服务器的日志会显示在终端中，包括：
- 请求日志
- 错误信息
- 智能体状态变化
- 系统事件

## 贡献指南

欢迎提交Issue和Pull Request来改进这个Web界面！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目采用MIT许可证。
